import redis
import joblib
import pickle
import io
import sys
import os
import time
import logging
from typing import Any
from gevent import monkey
from .serialization_manager import serialization_manager, SerializationMethod
from .protobuf_serializer import protobuf_serialization_manager, ExtendedSerializationMethod
from .redis_pool_manager import get_redis_client, redis_pool_manager
from .redis_streams_manager import get_streams_manager, StreamEventType
# 进行猴子补丁，替换阻塞的标准库函数为gevent协程版本
#monkey.patch_all()

# 配置日志
logger = logging.getLogger(__name__)
class RedisModelManager:
    def __init__(self, redis_host, redis_port, redis_password, redis_db,
                 serialization_method=ExtendedSerializationMethod.PROTOBUF_LZ4,
                 enable_fallback=True,
                 use_connection_pool=True,
                 enable_protobuf=True):

        self.redis_host = redis_host
        self.redis_port = redis_port
        self.redis_password = redis_password
        self.redis_db = redis_db
        self.serialization_method = serialization_method
        self.enable_fallback = enable_fallback
        self.use_connection_pool = use_connection_pool
        self.enable_protobuf = enable_protobuf

        # 选择序列化管理器
        if enable_protobuf:
            self.serializer = protobuf_serialization_manager
            logger.info("启用Protobuf序列化管理器")
        else:
            self.serializer = serialization_manager
            logger.info("使用传统序列化管理器")

        # 初始化Redis连接
        if use_connection_pool and redis_pool_manager:
            self.r = get_redis_client(redis_db)
            logger.info("使用连接池管理的Redis客户端")
        else:
            self.r = redis.StrictRedis(
                host=redis_host,
                port=redis_port,
                password=redis_password,
                db=redis_db,
                socket_keepalive=True,
                socket_keepalive_options={},
                retry_on_timeout=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            logger.info("使用传统Redis客户端")

        # 序列化管理器已在构造函数中设置

    def _serialize_data(self, data):
        """序列化数据"""
        try:
            return self.serializer.serialize(data, self.serialization_method)
        except Exception as e:
            logger.error(f"序列化失败: {e}")
            if self.enable_fallback:
                logger.warning("降级到Pickle序列化")
                return pickle.dumps(data)
            raise

    def _deserialize_data(self, data):
        """反序列化数据"""
        try:
            return self.serializer.deserialize(data)
        except Exception as e:
            logger.error(f"反序列化失败: {e}")
            if self.enable_fallback:
                logger.warning("降级到Pickle反序列化")
                return pickle.loads(data)
            raise

    def save_model(self, model_name, device_id, model, version):
        """保存模型到Redis（使用优化的序列化方式）"""
        try:
            # 序列化数据
            serialized_data = self._serialize_data(model)
            redis_key = f"{model_name}:{device_id}:{version}"

            # 存储到Redis
            self.r.set(redis_key, serialized_data)
            logger.debug(f"模型保存成功: {redis_key}, 数据大小: {len(serialized_data)} bytes")

            # 发送模型更新事件到流（如果启用）
            self._publish_model_update_event(model_name, device_id, version, model)

        except Exception as e:
            logger.error(f"模型保存失败: {e}")
            raise

    def load_model(self, model_name, device_id, version):
        """从Redis加载模型（自动检测格式并反序列化）"""
        try:
            redis_key = f"{model_name}:{device_id}:{version}"
            serialized_data = self.r.get(redis_key)

            if serialized_data is None:
                logger.warning(f"模型不存在: {redis_key}")
                return None

            # 反序列化数据
            model = self._deserialize_data(serialized_data)
            logger.debug(f"模型加载成功: {redis_key}")

            return model

        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            return None

    def get_serialization_stats(self):
        """获取序列化性能统计信息"""
        return self.serializer.get_stats()

    def reset_serialization_stats(self):
        """重置序列化统计信息"""
        self.serializer.reset_stats()

    def _publish_model_update_event(self, model_name: str, device_id: str, version: str, model_data: Any):
        """发布模型更新事件到流"""
        try:
            streams_manager = get_streams_manager()
            if streams_manager:
                event_data = {
                    'model_name': model_name,
                    'version': version,
                    'update_type': 'save',
                    'timestamp': time.time(),
                    'model_size': len(self._serialize_data(model_data))
                }

                streams_manager.produce_message(
                    StreamEventType.MODEL_UPDATE,
                    device_id,
                    event_data
                )
                logger.debug(f"模型更新事件已发布: {model_name}:{device_id}:{version}")
        except Exception as e:
            logger.warning(f"发布模型更新事件失败: {e}")

    def publish_prediction_request(self, model_name: str, device_id: str, input_data: Any, prediction_result: Any):
        """发布预测请求事件到流"""
        try:
            streams_manager = get_streams_manager()
            if streams_manager:
                event_data = {
                    'model_name': model_name,
                    'input_data': input_data,
                    'prediction_result': prediction_result,
                    'timestamp': time.time()
                }

                streams_manager.produce_message(
                    StreamEventType.PREDICTION_REQUEST,
                    device_id,
                    event_data
                )
                logger.debug(f"预测请求事件已发布: {model_name}:{device_id}")
        except Exception as e:
            logger.warning(f"发布预测请求事件失败: {e}")

    def publish_training_data(self, device_id: str, training_data: Any):
        """发布训练数据事件到流"""
        try:
            streams_manager = get_streams_manager()
            if streams_manager:
                event_data = {
                    'training_data': training_data,
                    'timestamp': time.time(),
                    'data_size': len(str(training_data))
                }

                streams_manager.produce_message(
                    StreamEventType.TRAINING_DATA,
                    device_id,
                    event_data
                )
                logger.debug(f"训练数据事件已发布: {device_id}")
        except Exception as e:
            logger.warning(f"发布训练数据事件失败: {e}")
    def delete_model(self, model_name, device_id):
        """删除 Redis 中指定设备的所有版本的模型"""
        all_keys = self.r.keys(f"{model_name}:{device_id}:*")  # 获取该设备的所有模型版本
        for key in all_keys:
            self.r.delete(key)  # 删除模型
            logger.info(f"删除模型：{key.decode()}")

    def delete_model_other(self, model_name, device_id):
        """删除 Redis 中该设备的所有其他模型（不包括当前模型）"""
        keys = self.r.keys(f"*:{device_id}:*")  # 获取该设备的所有存储记录
        for key in keys:
            decoded_key = key.decode()
            model_prefix = decoded_key.split(":")[0]  # 获取模型前缀
            gongxu = str(model_name).split('_')[0]
            if gongxu not in model_prefix:
                self.r.delete(decoded_key)
                logger.info(f"删除缓存：{decoded_key}")

    def check_model_exists(self, func_name, device_id, dict_model_key):
        """
        检查 Redis 中是否存在该模型，并返回对应的版本号
        """
        for key, values in dict_model_key.items():
            if func_name in values:
                # 查找符合 `model_name:device_id:*` 格式的 key
                pattern = f"{key}:{device_id}:*"
                matching_keys = self.r.keys(pattern)

                if matching_keys:
                    # 假设只有一个版本，则直接解析版本号
                    full_key = matching_keys[0].decode()  # 取出 key 并解码
                    version = full_key.split(":")[-1]  # 获取版本号
                    return True, version  # 返回存在状态和版本号

                return False, None  # 未找到模型
        return False, None  # 未匹配到函数名
