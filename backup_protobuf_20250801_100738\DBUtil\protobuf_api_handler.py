#!/usr/bin/env python3
"""
Protobuf API处理器
支持JSON和Protobuf双协议的API处理
"""

import logging
import time
import json
from typing import Any, Dict, Optional
from flask import Flask, request, jsonify, Response

from .protobuf_serializer import protobuf_serialization_manager, ExtendedSerializationMethod

logger = logging.getLogger(__name__)

# 尝试导入生成的Protobuf类
try:
    # 这些导入在proto文件编译后才可用
    from generated.python.industrial_control.v1 import common_pb2
    from generated.python.industrial_control.v1 import shouwei_service_pb2
    from generated.python.industrial_control.v1 import kongwen_service_pb2
    PROTOBUF_MESSAGES_AVAILABLE = True
except ImportError:
    PROTOBUF_MESSAGES_AVAILABLE = False
    logger.warning("Protobuf消息类未找到，请先编译proto文件")

class ProtobufAPIHandler:
    """Protobuf API处理器"""
    
    def __init__(self, app: Flask, redis_manager):
        self.app = app
        self.redis_manager = redis_manager
        self.serialization_manager = protobuf_serialization_manager
        
        # 注册Protobuf类型
        if PROTOBUF_MESSAGES_AVAILABLE:
            self._register_protobuf_types()
        
        # 设置路由
        self._setup_routes()
    
    def _register_protobuf_types(self):
        """注册Protobuf消息类型"""
        try:
            self.serialization_manager.register_protobuf_type(
                'shouwei_request', shouwei_service_pb2.ShouweiPredictionRequest
            )
            self.serialization_manager.register_protobuf_type(
                'shouwei_response', shouwei_service_pb2.ShouweiPredictionResponse
            )
            self.serialization_manager.register_protobuf_type(
                'kongwen_request', kongwen_service_pb2.KongwenPredictionRequest
            )
            self.serialization_manager.register_protobuf_type(
                'kongwen_response', kongwen_service_pb2.KongwenPredictionResponse
            )
            logger.info("Protobuf类型注册完成")
        except Exception as e:
            logger.error(f"Protobuf类型注册失败: {e}")
    
    def _setup_routes(self):
        """设置API路由"""
        
        @self.app.route('/api/v1/predict', methods=['POST'])
        def predict():
            """通用预测接口，支持双协议"""
            content_type = request.headers.get('Content-Type', '')
            
            try:
                if 'application/x-protobuf' in content_type:
                    return self._handle_protobuf_request()
                elif 'application/json' in content_type:
                    return self._handle_json_request()
                else:
                    return jsonify({'error': 'Unsupported content type'}), 400
            except Exception as e:
                logger.error(f"预测请求处理失败: {e}")
                return self._create_error_response(str(e), content_type)
        
        @self.app.route('/api/v1/shouwei/predict', methods=['POST'])
        def shouwei_predict():
            """首尾控制专用接口"""
            return self._handle_typed_request('shouwei')
        
        @self.app.route('/api/v1/kongwen/predict', methods=['POST'])
        def kongwen_predict():
            """控温专用接口"""
            return self._handle_typed_request('kongwen')
        
        @self.app.route('/api/v1/protocol/stats', methods=['GET'])
        def protocol_stats():
            """协议使用统计"""
            stats = self.serialization_manager.get_stats()
            return jsonify(stats)
    
    def _handle_protobuf_request(self):
        """处理Protobuf请求"""
        if not PROTOBUF_MESSAGES_AVAILABLE:
            return jsonify({'error': 'Protobuf支持不可用'}), 503
        
        try:
            # 解析请求数据
            request_data = request.get_data()
            
            # 自动检测请求类型
            device_type = self._detect_device_type_from_protobuf(request_data)
            
            if device_type == 'shouwei':
                return self._process_shouwei_protobuf(request_data)
            elif device_type == 'kongwen':
                return self._process_kongwen_protobuf(request_data)
            else:
                # 尝试通用处理
                return self._process_generic_protobuf(request_data)
                
        except Exception as e:
            logger.error(f"Protobuf请求处理失败: {e}")
            error_response = common_pb2.CommonResponse()
            error_response.success = False
            error_response.error_message = str(e)
            error_response.error_code = 400
            
            return Response(
                error_response.SerializeToString(),
                mimetype='application/x-protobuf',
                status=400
            )
    
    def _handle_json_request(self):
        """处理JSON请求（向后兼容）"""
        try:
            request_data = request.get_json()
            
            # 执行预测
            prediction_result = self._execute_prediction(request_data)
            
            return jsonify({
                'success': True,
                'prediction': prediction_result,
                'metadata': {
                    'protocol': 'json',
                    'processing_time_ms': prediction_result.get('processing_time_ms', 0)
                }
            })
            
        except Exception as e:
            logger.error(f"JSON请求处理失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 400
    
    def _handle_typed_request(self, device_type: str):
        """处理特定类型的请求"""
        content_type = request.headers.get('Content-Type', '')
        
        try:
            if 'application/x-protobuf' in content_type:
                request_data = request.get_data()
                if device_type == 'shouwei':
                    return self._process_shouwei_protobuf(request_data)
                elif device_type == 'kongwen':
                    return self._process_kongwen_protobuf(request_data)
            else:
                # JSON处理
                request_data = request.get_json()
                prediction_result = self._execute_prediction(request_data)
                return jsonify({
                    'success': True,
                    'prediction': prediction_result
                })
                
        except Exception as e:
            return self._create_error_response(str(e), content_type)
    
    def _process_shouwei_protobuf(self, request_data: bytes):
        """处理首尾控制Protobuf请求"""
        # 反序列化请求
        request_msg = shouwei_service_pb2.ShouweiPredictionRequest()
        request_msg.ParseFromString(request_data)
        
        # 转换为内部数据格式
        internal_data = self._protobuf_to_internal(request_msg)
        
        # 执行预测
        prediction_result = self._execute_prediction(internal_data)
        
        # 创建Protobuf响应
        response_msg = shouwei_service_pb2.ShouweiPredictionResponse()
        response_msg.success = True
        
        # 填充预测结果
        self._fill_shouwei_response(response_msg, prediction_result)
        
        return Response(
            response_msg.SerializeToString(),
            mimetype='application/x-protobuf'
        )
    
    def _process_kongwen_protobuf(self, request_data: bytes):
        """处理控温Protobuf请求"""
        # 反序列化请求
        request_msg = kongwen_service_pb2.KongwenPredictionRequest()
        request_msg.ParseFromString(request_data)
        
        # 转换为内部数据格式
        internal_data = self._protobuf_to_internal(request_msg)
        
        # 执行预测
        prediction_result = self._execute_prediction(internal_data)
        
        # 创建Protobuf响应
        response_msg = kongwen_service_pb2.KongwenPredictionResponse()
        response_msg.success = True
        
        # 填充预测结果
        self._fill_kongwen_response(response_msg, prediction_result)
        
        return Response(
            response_msg.SerializeToString(),
            mimetype='application/x-protobuf'
        )
    
    def _process_generic_protobuf(self, request_data: bytes):
        """处理通用Protobuf请求"""
        # 这里可以实现通用的Protobuf处理逻辑
        # 暂时返回错误
        error_response = common_pb2.CommonResponse()
        error_response.success = False
        error_response.error_message = "无法识别的Protobuf请求类型"
        error_response.error_code = 400
        
        return Response(
            error_response.SerializeToString(),
            mimetype='application/x-protobuf',
            status=400
        )
    
    def _detect_device_type_from_protobuf(self, request_data: bytes) -> str:
        """从Protobuf数据检测设备类型"""
        # 简单的启发式检测
        # 实际实现中可以根据消息结构进行更精确的检测
        try:
            # 尝试解析为首尾控制请求
            shouwei_msg = shouwei_service_pb2.ShouweiPredictionRequest()
            shouwei_msg.ParseFromString(request_data)
            if shouwei_msg.device_info.device_id:
                return 'shouwei'
        except:
            pass
        
        try:
            # 尝试解析为控温请求
            kongwen_msg = kongwen_service_pb2.KongwenPredictionRequest()
            kongwen_msg.ParseFromString(request_data)
            if kongwen_msg.device_info.device_id:
                return 'kongwen'
        except:
            pass
        
        return 'unknown'
    
    def _protobuf_to_internal(self, protobuf_msg) -> Dict[str, Any]:
        """Protobuf消息转换为内部数据格式"""
        from google.protobuf.json_format import MessageToDict
        return MessageToDict(protobuf_msg)
    
    def _execute_prediction(self, internal_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行预测逻辑"""
        start_time = time.time()
        
        # 这里调用现有的预测逻辑
        # 暂时返回模拟结果
        device_id = internal_data.get('device_info', {}).get('device_id', 'unknown')
        
        # 模拟预测结果
        result = {
            'device_id': device_id,
            'main_power': 600.0,
            'vice_power': 400.0,
            'total_power': 1000.0,
            'predicted_temperature': 26.5,
            'confidence': 0.95,
            'processing_time_ms': int((time.time() - start_time) * 1000)
        }
        
        return result
    
    def _fill_shouwei_response(self, response_msg, prediction_result: Dict[str, Any]):
        """填充首尾控制响应"""
        # 填充通用预测结果
        prediction = response_msg.prediction
        prediction.main_power = prediction_result.get('main_power', 0)
        prediction.vice_power = prediction_result.get('vice_power', 0)
        prediction.total_power = prediction_result.get('total_power', 0)
        prediction.predicted_temperature = prediction_result.get('predicted_temperature', 0)
        prediction.confidence = prediction_result.get('confidence', 0)
        
        # 填充首尾控制特有结果
        shouwei_result = response_msg.shouwei_result
        shouwei_result.lasu_efficiency = prediction_result.get('lasu_efficiency', 0.85)
        shouwei_result.stability_index = prediction_result.get('stability_index', 0.9)
        
        # 填充元数据
        metadata = prediction.metadata
        metadata.model_version = "v1.0"
        metadata.processing_time_ms = prediction_result.get('processing_time_ms', 0)
        metadata.cache_hit = prediction_result.get('cache_hit', False)
        metadata.serialization_method = "protobuf"
    
    def _fill_kongwen_response(self, response_msg, prediction_result: Dict[str, Any]):
        """填充控温响应"""
        # 填充通用预测结果
        prediction = response_msg.prediction
        prediction.main_power = prediction_result.get('main_power', 0)
        prediction.vice_power = prediction_result.get('vice_power', 0)
        prediction.total_power = prediction_result.get('total_power', 0)
        prediction.predicted_temperature = prediction_result.get('predicted_temperature', 0)
        prediction.confidence = prediction_result.get('confidence', 0)
        
        # 填充控温特有结果
        kongwen_result = response_msg.kongwen_result
        kongwen_result.required_heating_power = prediction_result.get('required_heating_power', 0)
        kongwen_result.required_cooling_power = prediction_result.get('required_cooling_power', 0)
        kongwen_result.temperature_rise_rate = prediction_result.get('temperature_rise_rate', 0)
        kongwen_result.settling_time = prediction_result.get('settling_time', 0)
        
        # 填充元数据
        metadata = prediction.metadata
        metadata.model_version = "v1.0"
        metadata.processing_time_ms = prediction_result.get('processing_time_ms', 0)
        metadata.cache_hit = prediction_result.get('cache_hit', False)
        metadata.serialization_method = "protobuf"
    
    def _create_error_response(self, error_message: str, content_type: str):
        """创建错误响应"""
        if 'application/x-protobuf' in content_type and PROTOBUF_MESSAGES_AVAILABLE:
            error_response = common_pb2.CommonResponse()
            error_response.success = False
            error_response.error_message = error_message
            error_response.error_code = 400
            
            return Response(
                error_response.SerializeToString(),
                mimetype='application/x-protobuf',
                status=400
            )
        else:
            return jsonify({
                'success': False,
                'error': error_message
            }), 400
