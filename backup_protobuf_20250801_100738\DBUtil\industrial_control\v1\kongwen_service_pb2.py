# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# 这是一个模拟的protobuf模块，用于演示集成

from .common_pb2 import DeviceInfo, SensorData, ControlParameters, PredictionResult

class KongwenSpecificData:
    def __init__(self):
        self.thermal_capacity = 0.0
        self.heat_transfer_coefficient = 0.0
        self.ambient_temperature = 0.0
        self.thermal_model = ThermalModel()

class ThermalModel:
    def __init__(self):
        self.time_constant = 0.0
        self.gain = 0.0
        self.dead_time = 0.0
        self.pid_parameters = []

class KongwenPredictionRequest:
    def __init__(self):
        self.device_info = DeviceInfo()
        self.sensor_data = SensorData()
        self.control_parameters = ControlParameters()
        self.kongwen_data = KongwenSpecificData()
    
    def SerializeToString(self):
        """模拟序列化方法"""
        import json
        data = {
            'device_info': {
                'device_id': self.device_info.device_id,
                'device_type': self.device_info.device_type,
                'location': self.device_info.location
            },
            'sensor_data': {
                'temperature': self.sensor_data.temperature,
                'pressure': self.sensor_data.pressure,
                'flow_rate': self.sensor_data.flow_rate,
                'power_consumption': self.sensor_data.power_consumption
            },
            'control_parameters': {
                'target_temperature': self.control_parameters.target_temperature,
                'max_power_limit': self.control_parameters.max_power_limit,
                'emergency_stop': self.control_parameters.emergency_stop
            },
            'kongwen_data': {
                'thermal_capacity': self.kongwen_data.thermal_capacity,
                'heat_transfer_coefficient': self.kongwen_data.heat_transfer_coefficient,
                'ambient_temperature': self.kongwen_data.ambient_temperature
            }
        }
        return json.dumps(data).encode('utf-8')
    
    def ParseFromString(self, data):
        """模拟反序列化方法"""
        import json
        parsed = json.loads(data.decode('utf-8'))
        
        # 填充设备信息
        device_info = parsed.get('device_info', {})
        self.device_info.device_id = device_info.get('device_id', '')
        self.device_info.device_type = device_info.get('device_type', '')
        self.device_info.location = device_info.get('location', '')
        
        # 填充传感器数据
        sensor_data = parsed.get('sensor_data', {})
        self.sensor_data.temperature = sensor_data.get('temperature', 0.0)
        self.sensor_data.pressure = sensor_data.get('pressure', 0.0)
        self.sensor_data.flow_rate = sensor_data.get('flow_rate', 0.0)
        self.sensor_data.power_consumption = sensor_data.get('power_consumption', 0.0)
        
        # 填充控制参数
        control_params = parsed.get('control_parameters', {})
        self.control_parameters.target_temperature = control_params.get('target_temperature', 0.0)
        self.control_parameters.max_power_limit = control_params.get('max_power_limit', 0)
        self.control_parameters.emergency_stop = control_params.get('emergency_stop', False)
        
        # 填充控温特有数据
        kongwen_data = parsed.get('kongwen_data', {})
        self.kongwen_data.thermal_capacity = kongwen_data.get('thermal_capacity', 0.0)
        self.kongwen_data.heat_transfer_coefficient = kongwen_data.get('heat_transfer_coefficient', 0.0)
        self.kongwen_data.ambient_temperature = kongwen_data.get('ambient_temperature', 0.0)

class KongwenSpecificResult:
    def __init__(self):
        self.required_heating_power = 0.0
        self.required_cooling_power = 0.0
        self.temperature_rise_rate = 0.0
        self.settling_time = 0.0
        self.optimized_model = ThermalModel()

class KongwenPredictionResponse:
    def __init__(self):
        self.success = False
        self.error_message = ""
        self.prediction = PredictionResult()
        self.kongwen_result = KongwenSpecificResult()
    
    def SerializeToString(self):
        """模拟序列化方法"""
        import json
        data = {
            'success': self.success,
            'error_message': self.error_message,
            'prediction': {
                'main_power': self.prediction.main_power,
                'vice_power': self.prediction.vice_power,
                'total_power': self.prediction.total_power,
                'predicted_temperature': self.prediction.predicted_temperature,
                'confidence': self.prediction.confidence
            },
            'kongwen_result': {
                'required_heating_power': self.kongwen_result.required_heating_power,
                'required_cooling_power': self.kongwen_result.required_cooling_power,
                'temperature_rise_rate': self.kongwen_result.temperature_rise_rate,
                'settling_time': self.kongwen_result.settling_time
            }
        }
        return json.dumps(data).encode('utf-8')
    
    def ParseFromString(self, data):
        """模拟反序列化方法"""
        import json
        parsed = json.loads(data.decode('utf-8'))
        self.success = parsed.get('success', False)
        self.error_message = parsed.get('error_message', '')
        
        # 填充预测结果
        prediction = parsed.get('prediction', {})
        self.prediction.main_power = prediction.get('main_power', 0.0)
        self.prediction.vice_power = prediction.get('vice_power', 0.0)
        self.prediction.total_power = prediction.get('total_power', 0.0)
        self.prediction.predicted_temperature = prediction.get('predicted_temperature', 0.0)
        self.prediction.confidence = prediction.get('confidence', 0.0)
        
        # 填充控温特有结果
        kongwen_result = parsed.get('kongwen_result', {})
        self.kongwen_result.required_heating_power = kongwen_result.get('required_heating_power', 0.0)
        self.kongwen_result.required_cooling_power = kongwen_result.get('required_cooling_power', 0.0)
        self.kongwen_result.temperature_rise_rate = kongwen_result.get('temperature_rise_rate', 0.0)
        self.kongwen_result.settling_time = kongwen_result.get('settling_time', 0.0)

class ThermalModelResponse:
    def __init__(self):
        self.success = False
        self.thermal_model = ThermalModel()
        self.model_accuracy = 0.0
        self.calibration_time = None

class PIDOptimizationRequest:
    def __init__(self):
        self.device_info = DeviceInfo()
        self.current_model = ThermalModel()
        self.target_response = []

class PIDOptimizationResponse:
    def __init__(self):
        self.success = False
        self.error_message = ""
        self.optimized_pid = []
        self.improvement_percentage = 0.0
