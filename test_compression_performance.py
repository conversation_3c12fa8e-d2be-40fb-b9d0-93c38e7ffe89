#!/usr/bin/env python3
"""
MessagePack + LZ4 压缩性能测试脚本
演示压缩传输的完整流程和性能对比
"""

import pickle
import msgpack
import lz4.frame
import time
import sys
import numpy as np
from typing import Dict, Any, Tuple

class CompressionBenchmark:
    """压缩性能基准测试类"""
    
    def __init__(self):
        self.results = {}
    
    def create_test_model_data(self, size_mb: float = 1.0) -> Dict[str, Any]:
        """创建测试用的模型数据"""
        # 模拟一个机器学习模型的数据结构
        num_features = int(size_mb * 1024 * 1024 / 8 / 100)  # 估算特征数量
        
        model_data = {
            'model_type': 'neural_network',
            'version': 'v1.0',
            'weights': {
                'layer1': np.random.randn(num_features, 128).tolist(),
                'layer2': np.random.randn(128, 64).tolist(),
                'layer3': np.random.randn(64, 1).tolist()
            },
            'biases': {
                'layer1': np.random.randn(128).tolist(),
                'layer2': np.random.randn(64).tolist(),
                'layer3': np.random.randn(1).tolist()
            },
            'metadata': {
                'training_accuracy': 0.95,
                'validation_accuracy': 0.92,
                'epochs': 100,
                'learning_rate': 0.001,
                'batch_size': 32,
                'optimizer': 'adam',
                'loss_function': 'mse',
                'features': [f'feature_{i}' for i in range(num_features)]
            },
            'hyperparameters': {
                'dropout_rate': 0.2,
                'regularization': 0.01,
                'activation': 'relu'
            }
        }
        return model_data
    
    def test_pickle_serialization(self, data: Any) -> Tuple[bytes, float, float]:
        """测试Pickle序列化性能"""
        # 序列化
        start_time = time.time()
        serialized = pickle.dumps(data)
        serialize_time = time.time() - start_time
        
        # 反序列化
        start_time = time.time()
        deserialized = pickle.loads(serialized)
        deserialize_time = time.time() - start_time
        
        return serialized, serialize_time, deserialize_time
    
    def test_msgpack_serialization(self, data: Any) -> Tuple[bytes, float, float]:
        """测试MessagePack序列化性能"""
        # 序列化
        start_time = time.time()
        serialized = msgpack.packb(data, use_bin_type=True)
        serialize_time = time.time() - start_time
        
        # 反序列化
        start_time = time.time()
        deserialized = msgpack.unpackb(serialized, raw=False)
        deserialize_time = time.time() - start_time
        
        return serialized, serialize_time, deserialize_time
    
    def test_msgpack_lz4_compression(self, data: Any) -> Tuple[bytes, float, float]:
        """测试MessagePack + LZ4压缩性能"""
        # 序列化 + 压缩
        start_time = time.time()
        serialized = msgpack.packb(data, use_bin_type=True)
        compressed = lz4.frame.compress(serialized)
        compress_time = time.time() - start_time
        
        # 解压缩 + 反序列化
        start_time = time.time()
        decompressed = lz4.frame.decompress(compressed)
        deserialized = msgpack.unpackb(decompressed, raw=False)
        decompress_time = time.time() - start_time
        
        return compressed, compress_time, decompress_time
    
    def run_benchmark(self, model_sizes: list = [0.1, 0.5, 1.0, 2.0, 5.0]):
        """运行完整的性能基准测试"""
        print("=" * 80)
        print("MessagePack + LZ4 压缩性能基准测试")
        print("=" * 80)
        
        for size_mb in model_sizes:
            print(f"\n测试模型大小: {size_mb} MB")
            print("-" * 60)
            
            # 创建测试数据
            test_data = self.create_test_model_data(size_mb)
            
            # 测试Pickle
            pickle_data, pickle_ser_time, pickle_deser_time = self.test_pickle_serialization(test_data)
            pickle_size = len(pickle_data)
            
            # 测试MessagePack
            msgpack_data, msgpack_ser_time, msgpack_deser_time = self.test_msgpack_serialization(test_data)
            msgpack_size = len(msgpack_data)
            
            # 测试MessagePack + LZ4
            compressed_data, compress_time, decompress_time = self.test_msgpack_lz4_compression(test_data)
            compressed_size = len(compressed_data)
            
            # 计算压缩比和性能提升
            compression_ratio = compressed_size / pickle_size
            size_reduction = (1 - compression_ratio) * 100
            
            # 输出结果
            print(f"Pickle序列化:")
            print(f"  数据大小: {pickle_size:,} bytes")
            print(f"  序列化时间: {pickle_ser_time*1000:.2f} ms")
            print(f"  反序列化时间: {pickle_deser_time*1000:.2f} ms")
            print(f"  总时间: {(pickle_ser_time + pickle_deser_time)*1000:.2f} ms")
            
            print(f"\nMessagePack序列化:")
            print(f"  数据大小: {msgpack_size:,} bytes")
            print(f"  序列化时间: {msgpack_ser_time*1000:.2f} ms")
            print(f"  反序列化时间: {msgpack_deser_time*1000:.2f} ms")
            print(f"  总时间: {(msgpack_ser_time + msgpack_deser_time)*1000:.2f} ms")
            
            print(f"\nMessagePack + LZ4压缩:")
            print(f"  压缩后大小: {compressed_size:,} bytes")
            print(f"  压缩时间: {compress_time*1000:.2f} ms")
            print(f"  解压时间: {decompress_time*1000:.2f} ms")
            print(f"  总时间: {(compress_time + decompress_time)*1000:.2f} ms")
            
            print(f"\n性能对比:")
            print(f"  压缩比: {compression_ratio:.2f} ({size_reduction:.1f}% 减少)")
            print(f"  速度提升: {((pickle_ser_time + pickle_deser_time) / (compress_time + decompress_time)):.2f}x")
            
            # 模拟网络传输时间 (假设100Mbps带宽)
            network_speed_mbps = 100
            network_speed_bytes_per_ms = network_speed_mbps * 1024 * 1024 / 8 / 1000
            
            pickle_transfer_time = pickle_size / network_speed_bytes_per_ms
            compressed_transfer_time = compressed_size / network_speed_bytes_per_ms
            
            print(f"\n网络传输时间 (100Mbps):")
            print(f"  Pickle传输: {pickle_transfer_time:.2f} ms")
            print(f"  压缩传输: {compressed_transfer_time:.2f} ms")
            print(f"  传输时间节省: {pickle_transfer_time - compressed_transfer_time:.2f} ms")
            
            # 计算端到端延迟
            pickle_total = (pickle_ser_time + pickle_deser_time) * 1000 + pickle_transfer_time
            compressed_total = (compress_time + decompress_time) * 1000 + compressed_transfer_time
            
            print(f"\n端到端延迟:")
            print(f"  Pickle方案: {pickle_total:.2f} ms")
            print(f"  压缩方案: {compressed_total:.2f} ms")
            print(f"  延迟减少: {pickle_total - compressed_total:.2f} ms ({((pickle_total - compressed_total) / pickle_total * 100):.1f}%)")

def demonstrate_compression_flow():
    """演示压缩传输的完整数据流"""
    print("\n" + "=" * 80)
    print("压缩传输数据流演示")
    print("=" * 80)
    
    # 创建示例数据
    sample_data = {
        'device_id': 'device_001',
        'model_weights': np.random.randn(1000, 100).tolist(),
        'timestamp': time.time(),
        'metadata': {'version': 'v1.0', 'accuracy': 0.95}
    }
    
    print("1. 原始数据 (Python对象)")
    print(f"   类型: {type(sample_data)}")
    print(f"   键: {list(sample_data.keys())}")
    
    print("\n2. MessagePack序列化")
    serialized = msgpack.packb(sample_data, use_bin_type=True)
    print(f"   序列化后大小: {len(serialized):,} bytes")
    print(f"   数据类型: {type(serialized)}")
    
    print("\n3. LZ4压缩")
    compressed = lz4.frame.compress(serialized)
    print(f"   压缩后大小: {len(compressed):,} bytes")
    print(f"   压缩比: {len(compressed)/len(serialized):.2f}")
    print(f"   数据类型: {type(compressed)}")
    
    print("\n4. 网络传输 (模拟)")
    print("   [发送端] → 网络 → [接收端]")
    print(f"   传输数据: {len(compressed):,} bytes")
    
    print("\n5. LZ4解压缩")
    decompressed = lz4.frame.decompress(compressed)
    print(f"   解压后大小: {len(decompressed):,} bytes")
    print(f"   数据完整性: {'✓' if decompressed == serialized else '✗'}")
    
    print("\n6. MessagePack反序列化")
    final_data = msgpack.unpackb(decompressed, raw=False)
    print(f"   最终数据类型: {type(final_data)}")
    print(f"   数据完整性: {'✓' if final_data['device_id'] == sample_data['device_id'] else '✗'}")
    
    print("\n✓ 压缩传输流程完成!")

if __name__ == "__main__":
    # 演示压缩传输流程
    demonstrate_compression_flow()
    
    # 运行性能基准测试
    benchmark = CompressionBenchmark()
    benchmark.run_benchmark([0.1, 0.5, 1.0])
    
    print("\n" + "=" * 80)
    print("总结:")
    print("- MessagePack + LZ4 提供了更好的压缩比和性能")
    print("- 减少了网络传输时间和存储空间")
    print("- 序列化/反序列化速度更快")
    print("- 支持跨语言兼容性")
    print("=" * 80)
