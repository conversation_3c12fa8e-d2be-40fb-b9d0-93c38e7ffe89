#!/usr/bin/env python3
"""
手动编译proto文件脚本
当protoc安装完成后使用此脚本编译proto文件
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_protoc():
    """检查protoc是否可用"""
    try:
        result = subprocess.run(['protoc', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info(f"找到protoc: {result.stdout.strip()}")
            return True
        else:
            logger.error("protoc不可用")
            return False
    except FileNotFoundError:
        logger.error("protoc未安装或不在PATH中")
        return False

def compile_proto_files():
    """编译proto文件"""
    project_root = Path(__file__).parent.parent
    proto_dir = project_root / "proto"
    output_dir = project_root / "generated" / "python"
    
    # 确保输出目录存在
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 查找所有proto文件
    proto_files = list(proto_dir.rglob("*.proto"))
    
    if not proto_files:
        logger.warning("未找到proto文件")
        return False
    
    logger.info(f"找到 {len(proto_files)} 个proto文件")
    
    # 编译每个proto文件
    success_count = 0
    for proto_file in proto_files:
        logger.info(f"编译: {proto_file.name}")
        
        try:
            # 使用protoc编译
            cmd = [
                'protoc',
                f'--proto_path={proto_dir}',
                f'--python_out={output_dir}',
                str(proto_file.relative_to(proto_dir))
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=project_root)
            
            if result.returncode == 0:
                logger.info(f"✓ 编译成功: {proto_file.name}")
                success_count += 1
            else:
                logger.error(f"✗ 编译失败 {proto_file.name}: {result.stderr}")
        
        except Exception as e:
            logger.error(f"✗ 编译异常 {proto_file.name}: {e}")
    
    logger.info(f"编译完成: {success_count}/{len(proto_files)} 成功")
    return success_count == len(proto_files)

def create_init_files():
    """创建__init__.py文件"""
    project_root = Path(__file__).parent.parent
    generated_dir = project_root / "generated" / "python"
    
    # 需要创建__init__.py的目录
    init_dirs = [
        generated_dir,
        generated_dir / "industrial_control",
        generated_dir / "industrial_control" / "v1"
    ]
    
    for init_dir in init_dirs:
        if init_dir.exists():
            init_file = init_dir / "__init__.py"
            if not init_file.exists():
                init_file.write_text("# Generated protobuf package\n")
                logger.info(f"创建: {init_file}")

def copy_to_dbutil():
    """复制生成的文件到DBUtil目录"""
    project_root = Path(__file__).parent.parent
    source_dir = project_root / "generated" / "python" / "industrial_control"
    target_dir = project_root / "DBUtil" / "industrial_control"
    
    if source_dir.exists():
        import shutil
        if target_dir.exists():
            shutil.rmtree(target_dir)
        shutil.copytree(source_dir, target_dir)
        logger.info(f"复制完成: {source_dir} -> {target_dir}")
        return True
    else:
        logger.warning("生成的文件目录不存在")
        return False

def verify_compilation():
    """验证编译结果"""
    try:
        project_root = Path(__file__).parent.parent
        sys.path.insert(0, str(project_root))
        
        from DBUtil.industrial_control.v1 import common_pb2
        from DBUtil.industrial_control.v1 import shouwei_service_pb2
        from DBUtil.industrial_control.v1 import kongwen_service_pb2
        
        # 测试创建消息
        device_info = common_pb2.DeviceInfo()
        device_info.device_id = "test_device"
        
        # 测试序列化
        serialized = device_info.SerializeToString()
        
        logger.info("✓ 编译验证成功")
        return True
        
    except ImportError as e:
        logger.error(f"✗ 编译验证失败: {e}")
        return False

def main():
    """主函数"""
    print("手动编译proto文件")
    print("="*40)
    
    # 1. 检查protoc
    if not check_protoc():
        print("\n❌ protoc不可用")
        print("\n请先安装protoc:")
        print("1. 下载: https://github.com/protocolbuffers/protobuf/releases")
        print("2. 解压到: C:\\protoc\\")
        print("3. 添加到PATH: C:\\protoc\\bin")
        print("4. 重新打开命令行")
        return False
    
    # 2. 编译proto文件
    if not compile_proto_files():
        print("\n❌ proto文件编译失败")
        return False
    
    # 3. 创建__init__.py文件
    create_init_files()
    
    # 4. 复制到DBUtil
    if not copy_to_dbutil():
        print("\n❌ 文件复制失败")
        return False
    
    # 5. 验证编译结果
    if not verify_compilation():
        print("\n❌ 编译验证失败")
        return False
    
    print("\n🎉 proto文件编译成功！")
    print("\n现在您可以:")
    print("1. 使用真正的Protobuf消息")
    print("2. 运行完整的Protobuf测试")
    print("3. 启用高性能Protobuf序列化")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
