#!/usr/bin/env python3
"""
Proto文件编译脚本
编译proto文件并生成Python代码
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_protoc_available():
    """检查protoc编译器是否可用"""
    try:
        result = subprocess.run(['protoc', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info(f"找到protoc编译器: {result.stdout.strip()}")
            return True
        else:
            logger.error("protoc编译器不可用")
            return False
    except FileNotFoundError:
        logger.error("protoc编译器未安装")
        return False

def install_protobuf_dependencies():
    """安装Protobuf依赖"""
    logger.info("安装Protobuf依赖...")
    
    dependencies = [
        'protobuf>=4.21.0',
        'grpcio>=1.50.0',
        'grpcio-tools>=1.50.0'
    ]
    
    for dep in dependencies:
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', dep], 
                         check=True, capture_output=True)
            logger.info(f"成功安装: {dep}")
        except subprocess.CalledProcessError as e:
            logger.error(f"安装失败 {dep}: {e}")
            return False
    
    return True

def compile_proto_files():
    """编译proto文件"""
    logger.info("开始编译proto文件...")
    
    # 项目根目录
    project_root = Path(__file__).parent.parent
    proto_dir = project_root / "proto"
    output_dir = project_root / "generated" / "python"
    
    # 确保输出目录存在
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 查找所有proto文件
    proto_files = list(proto_dir.rglob("*.proto"))
    
    if not proto_files:
        logger.warning("未找到proto文件")
        return False
    
    logger.info(f"找到 {len(proto_files)} 个proto文件")
    
    # 编译每个proto文件
    for proto_file in proto_files:
        logger.info(f"编译: {proto_file}")
        
        try:
            cmd = [
                'protoc',
                f'--proto_path={proto_dir}',
                f'--python_out={output_dir}',
                f'--grpc_python_out={output_dir}',
                str(proto_file)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=project_root)
            
            if result.returncode == 0:
                logger.info(f"编译成功: {proto_file.name}")
            else:
                logger.error(f"编译失败 {proto_file.name}: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"编译异常 {proto_file.name}: {e}")
            return False
    
    return True

def create_init_files():
    """创建__init__.py文件"""
    logger.info("创建__init__.py文件...")
    
    project_root = Path(__file__).parent.parent
    generated_dir = project_root / "generated" / "python"
    
    # 需要创建__init__.py的目录
    init_dirs = [
        generated_dir,
        generated_dir / "industrial_control",
        generated_dir / "industrial_control" / "v1"
    ]
    
    for init_dir in init_dirs:
        if init_dir.exists():
            init_file = init_dir / "__init__.py"
            if not init_file.exists():
                init_file.write_text("# Generated protobuf package\n")
                logger.info(f"创建: {init_file}")

def copy_generated_files():
    """复制生成的文件到DBUtil目录"""
    logger.info("复制生成的文件...")
    
    project_root = Path(__file__).parent.parent
    source_dir = project_root / "generated" / "python" / "industrial_control"
    target_dir = project_root / "DBUtil" / "industrial_control"
    
    if source_dir.exists():
        import shutil
        if target_dir.exists():
            shutil.rmtree(target_dir)
        shutil.copytree(source_dir, target_dir)
        logger.info(f"复制完成: {source_dir} -> {target_dir}")
    else:
        logger.warning("生成的文件目录不存在")

def verify_compilation():
    """验证编译结果"""
    logger.info("验证编译结果...")
    
    try:
        # 尝试导入生成的模块
        sys.path.insert(0, str(Path(__file__).parent.parent))
        
        from DBUtil.industrial_control.v1 import common_pb2
        from DBUtil.industrial_control.v1 import shouwei_service_pb2
        from DBUtil.industrial_control.v1 import kongwen_service_pb2
        
        logger.info("✓ 所有Protobuf模块导入成功")
        
        # 测试创建消息
        device_info = common_pb2.DeviceInfo()
        device_info.device_id = "test_device"
        device_info.device_type = "shouwei_lasu_control"
        
        logger.info("✓ Protobuf消息创建成功")
        
        return True
        
    except ImportError as e:
        logger.error(f"✗ 模块导入失败: {e}")
        return False
    except Exception as e:
        logger.error(f"✗ 验证失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始Protobuf编译流程...")
    
    # 1. 检查protoc编译器
    if not check_protoc_available():
        logger.error("请先安装protoc编译器")
        logger.info("安装方法:")
        logger.info("  Ubuntu/Debian: sudo apt-get install protobuf-compiler")
        logger.info("  macOS: brew install protobuf")
        logger.info("  Windows: 下载预编译版本 https://github.com/protocolbuffers/protobuf/releases")
        return False
    
    # 2. 安装Python依赖
    if not install_protobuf_dependencies():
        logger.error("依赖安装失败")
        return False
    
    # 3. 编译proto文件
    if not compile_proto_files():
        logger.error("proto文件编译失败")
        return False
    
    # 4. 创建__init__.py文件
    create_init_files()
    
    # 5. 复制生成的文件
    copy_generated_files()
    
    # 6. 验证编译结果
    if not verify_compilation():
        logger.error("编译验证失败")
        return False
    
    logger.info("🎉 Protobuf编译完成！")
    logger.info("现在可以使用Protobuf功能了")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
