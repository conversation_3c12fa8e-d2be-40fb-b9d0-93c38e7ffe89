# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from industrial_control.v1 import common_pb2 as industrial__control_dot_v1_dot_common__pb2
from industrial_control.v1 import kongwen_service_pb2 as industrial__control_dot_v1_dot_kongwen__service__pb2

GRPC_GENERATED_VERSION = '1.74.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in industrial_control/v1/kongwen_service_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class KongwenControlServiceStub(object):
    """控温服务
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Predict = channel.unary_unary(
                '/industrial_control.v1.KongwenControlService/Predict',
                request_serializer=industrial__control_dot_v1_dot_kongwen__service__pb2.KongwenPredictionRequest.SerializeToString,
                response_deserializer=industrial__control_dot_v1_dot_kongwen__service__pb2.KongwenPredictionResponse.FromString,
                _registered_method=True)
        self.GetThermalModel = channel.unary_unary(
                '/industrial_control.v1.KongwenControlService/GetThermalModel',
                request_serializer=industrial__control_dot_v1_dot_common__pb2.DeviceInfo.SerializeToString,
                response_deserializer=industrial__control_dot_v1_dot_kongwen__service__pb2.ThermalModelResponse.FromString,
                _registered_method=True)
        self.OptimizePID = channel.unary_unary(
                '/industrial_control.v1.KongwenControlService/OptimizePID',
                request_serializer=industrial__control_dot_v1_dot_kongwen__service__pb2.PIDOptimizationRequest.SerializeToString,
                response_deserializer=industrial__control_dot_v1_dot_kongwen__service__pb2.PIDOptimizationResponse.FromString,
                _registered_method=True)


class KongwenControlServiceServicer(object):
    """控温服务
    """

    def Predict(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetThermalModel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def OptimizePID(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_KongwenControlServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Predict': grpc.unary_unary_rpc_method_handler(
                    servicer.Predict,
                    request_deserializer=industrial__control_dot_v1_dot_kongwen__service__pb2.KongwenPredictionRequest.FromString,
                    response_serializer=industrial__control_dot_v1_dot_kongwen__service__pb2.KongwenPredictionResponse.SerializeToString,
            ),
            'GetThermalModel': grpc.unary_unary_rpc_method_handler(
                    servicer.GetThermalModel,
                    request_deserializer=industrial__control_dot_v1_dot_common__pb2.DeviceInfo.FromString,
                    response_serializer=industrial__control_dot_v1_dot_kongwen__service__pb2.ThermalModelResponse.SerializeToString,
            ),
            'OptimizePID': grpc.unary_unary_rpc_method_handler(
                    servicer.OptimizePID,
                    request_deserializer=industrial__control_dot_v1_dot_kongwen__service__pb2.PIDOptimizationRequest.FromString,
                    response_serializer=industrial__control_dot_v1_dot_kongwen__service__pb2.PIDOptimizationResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'industrial_control.v1.KongwenControlService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('industrial_control.v1.KongwenControlService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class KongwenControlService(object):
    """控温服务
    """

    @staticmethod
    def Predict(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/industrial_control.v1.KongwenControlService/Predict',
            industrial__control_dot_v1_dot_kongwen__service__pb2.KongwenPredictionRequest.SerializeToString,
            industrial__control_dot_v1_dot_kongwen__service__pb2.KongwenPredictionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetThermalModel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/industrial_control.v1.KongwenControlService/GetThermalModel',
            industrial__control_dot_v1_dot_common__pb2.DeviceInfo.SerializeToString,
            industrial__control_dot_v1_dot_kongwen__service__pb2.ThermalModelResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def OptimizePID(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/industrial_control.v1.KongwenControlService/OptimizePID',
            industrial__control_dot_v1_dot_kongwen__service__pb2.PIDOptimizationRequest.SerializeToString,
            industrial__control_dot_v1_dot_kongwen__service__pb2.PIDOptimizationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
