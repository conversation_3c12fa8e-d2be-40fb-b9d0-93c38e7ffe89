#!/usr/bin/env python3
"""
序列化优化测试运行器
运行所有相关的测试并生成综合报告
"""

import sys
import os
import time
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Any

def run_command(cmd: List[str], cwd: str = None) -> Dict[str, Any]:
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            cwd=cwd,
            timeout=300  # 5分钟超时
        )
        return {
            'success': result.returncode == 0,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'returncode': result.returncode
        }
    except subprocess.TimeoutExpired:
        return {
            'success': False,
            'stdout': '',
            'stderr': '命令执行超时',
            'returncode': -1
        }
    except Exception as e:
        return {
            'success': False,
            'stdout': '',
            'stderr': str(e),
            'returncode': -1
        }

def check_dependencies() -> Dict[str, bool]:
    """检查依赖库是否安装"""
    dependencies = {
        'msgpack': False,
        'lz4': False,
        'numpy': False,
        'redis': False,
        'flask': False
    }
    
    for dep in dependencies:
        try:
            __import__(dep)
            dependencies[dep] = True
        except ImportError:
            dependencies[dep] = False
    
    return dependencies

def run_performance_tests() -> Dict[str, Any]:
    """运行性能测试"""
    print("=" * 60)
    print("运行性能测试...")
    print("=" * 60)
    
    test_file = "tests/performance/test_model_transmission_performance.py"
    
    if not Path(test_file).exists():
        return {
            'success': False,
            'error': f'测试文件不存在: {test_file}'
        }
    
    result = run_command([sys.executable, test_file])
    
    if result['success']:
        print("✓ 性能测试通过")
        print(result['stdout'])
    else:
        print("✗ 性能测试失败")
        print(result['stderr'])
    
    return result

def run_integration_tests() -> Dict[str, Any]:
    """运行集成测试"""
    print("\n" + "=" * 60)
    print("运行集成测试...")
    print("=" * 60)
    
    test_file = "tests/integration/test_serialization_integration.py"
    
    if not Path(test_file).exists():
        return {
            'success': False,
            'error': f'测试文件不存在: {test_file}'
        }
    
    result = run_command([sys.executable, test_file])
    
    if result['success']:
        print("✓ 集成测试通过")
        print(result['stdout'])
    else:
        print("✗ 集成测试失败")
        print(result['stderr'])
    
    return result

def run_unit_tests() -> Dict[str, Any]:
    """运行单元测试"""
    print("\n" + "=" * 60)
    print("运行单元测试...")
    print("=" * 60)
    
    # 检查是否安装了pytest
    try:
        import pytest
        cmd = [sys.executable, "-m", "pytest", "tests/", "-v", "--tb=short"]
    except ImportError:
        # 降级到unittest
        cmd = [sys.executable, "-m", "unittest", "discover", "tests", "-v"]
    
    result = run_command(cmd)
    
    if result['success']:
        print("✓ 单元测试通过")
    else:
        print("✗ 单元测试失败")
        print(result['stderr'])
    
    return result

def test_serialization_basic() -> Dict[str, Any]:
    """基础序列化功能测试"""
    print("\n" + "=" * 60)
    print("基础序列化功能测试...")
    print("=" * 60)
    
    try:
        # 导入序列化管理器
        sys.path.insert(0, '.')
        from DBUtil.serialization_manager import SerializationManager, SerializationMethod
        
        # 创建测试数据
        test_data = {
            'model_type': 'test_model',
            'device_id': 'test_device',
            'data': [1, 2, 3, 4, 5],
            'metadata': {'version': '1.0', 'accuracy': 0.95}
        }
        
        serializer = SerializationManager()
        
        # 测试不同序列化方法
        methods = [
            (SerializationMethod.PICKLE, "Pickle"),
            (SerializationMethod.MSGPACK, "MessagePack"),
            (SerializationMethod.MSGPACK_LZ4, "MessagePack+LZ4")
        ]
        
        results = {}
        
        for method, name in methods:
            try:
                start_time = time.time()
                serialized = serializer.serialize(test_data, method)
                serialize_time = time.time() - start_time
                
                start_time = time.time()
                deserialized = serializer.deserialize(serialized, method)
                deserialize_time = time.time() - start_time
                
                # 验证数据完整性
                assert deserialized['model_type'] == test_data['model_type']
                assert deserialized['device_id'] == test_data['device_id']
                
                results[name] = {
                    'success': True,
                    'serialize_time_ms': serialize_time * 1000,
                    'deserialize_time_ms': deserialize_time * 1000,
                    'data_size': len(serialized)
                }
                
                print(f"✓ {name}: {len(serialized)} bytes, "
                      f"{serialize_time*1000:.2f}ms + {deserialize_time*1000:.2f}ms")
                
            except ImportError:
                results[name] = {
                    'success': False,
                    'error': '库未安装'
                }
                print(f"- {name}: 库未安装，跳过测试")
            except Exception as e:
                results[name] = {
                    'success': False,
                    'error': str(e)
                }
                print(f"✗ {name}: {e}")
        
        # 测试自动格式检测
        try:
            pickle_data = serializer.serialize(test_data, SerializationMethod.PICKLE)
            auto_detected = serializer.deserialize(pickle_data)
            assert auto_detected['model_type'] == test_data['model_type']
            print("✓ 自动格式检测正常")
        except Exception as e:
            print(f"✗ 自动格式检测失败: {e}")
        
        return {
            'success': True,
            'results': results
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def generate_test_report(results: Dict[str, Any]) -> str:
    """生成测试报告"""
    report = []
    report.append("# 序列化优化测试报告")
    report.append(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # 依赖检查结果
    report.append("## 依赖库检查")
    deps = results.get('dependencies', {})
    for dep, installed in deps.items():
        status = "✓" if installed else "✗"
        report.append(f"- {dep}: {status}")
    report.append("")
    
    # 基础功能测试
    basic_test = results.get('basic_test', {})
    if basic_test.get('success'):
        report.append("## 基础序列化功能测试")
        report.append("| 方法 | 状态 | 数据大小 | 序列化时间 | 反序列化时间 |")
        report.append("|------|------|----------|------------|--------------|")
        
        for method, result in basic_test.get('results', {}).items():
            if result.get('success'):
                size = result.get('data_size', 0)
                ser_time = result.get('serialize_time_ms', 0)
                deser_time = result.get('deserialize_time_ms', 0)
                report.append(f"| {method} | ✓ | {size} bytes | {ser_time:.2f}ms | {deser_time:.2f}ms |")
            else:
                error = result.get('error', '未知错误')
                report.append(f"| {method} | ✗ | - | - | {error} |")
        report.append("")
    
    # 测试结果汇总
    report.append("## 测试结果汇总")
    test_types = ['performance_test', 'integration_test', 'unit_test']
    
    for test_type in test_types:
        test_result = results.get(test_type, {})
        status = "✓ 通过" if test_result.get('success') else "✗ 失败"
        test_name = test_type.replace('_', ' ').title()
        report.append(f"- {test_name}: {status}")
        
        if not test_result.get('success') and 'error' in test_result:
            report.append(f"  错误: {test_result['error']}")
    
    report.append("")
    
    # 建议
    report.append("## 建议")
    failed_deps = [dep for dep, installed in deps.items() if not installed]
    if failed_deps:
        report.append("### 安装缺失的依赖库")
        report.append("```bash")
        report.append(f"pip install {' '.join(failed_deps)}")
        report.append("```")
        report.append("")
    
    failed_tests = [test for test in test_types if not results.get(test, {}).get('success')]
    if failed_tests:
        report.append("### 修复失败的测试")
        for test in failed_tests:
            report.append(f"- 检查 {test} 的错误信息并修复相关问题")
        report.append("")
    
    if not failed_deps and not failed_tests:
        report.append("✓ 所有测试通过，序列化优化已成功实施！")
    
    return "\n".join(report)

def main():
    """主函数"""
    print("序列化优化测试运行器")
    print("=" * 60)
    
    # 收集所有测试结果
    results = {}
    
    # 检查依赖
    print("检查依赖库...")
    results['dependencies'] = check_dependencies()
    
    # 基础功能测试
    results['basic_test'] = test_serialization_basic()
    
    # 性能测试
    results['performance_test'] = run_performance_tests()
    
    # 集成测试
    results['integration_test'] = run_integration_tests()
    
    # 单元测试
    results['unit_test'] = run_unit_tests()
    
    # 生成报告
    print("\n" + "=" * 60)
    print("生成测试报告...")
    print("=" * 60)
    
    report = generate_test_report(results)
    
    # 保存报告到文件
    report_file = f"test_report_{int(time.time())}.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"测试报告已保存到: {report_file}")
    print("\n报告内容:")
    print(report)
    
    # 返回总体结果
    all_success = all(
        results.get(test, {}).get('success', False) 
        for test in ['basic_test', 'performance_test', 'integration_test', 'unit_test']
    )
    
    if all_success:
        print("\n🎉 所有测试通过！序列化优化实施成功！")
        sys.exit(0)
    else:
        print("\n⚠️  部分测试失败，请检查报告中的错误信息。")
        sys.exit(1)

if __name__ == "__main__":
    main()
