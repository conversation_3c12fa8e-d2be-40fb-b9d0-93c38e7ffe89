#!/usr/bin/env python3
"""
Protobuf基础功能测试
不依赖Redis的基础测试
"""

import unittest
import sys
import os
import time
import json

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from DBUtil.protobuf_serializer import protobuf_serialization_manager, ExtendedSerializationMethod

class TestProtobufBasic(unittest.TestCase):
    """测试Protobuf基础功能"""
    
    def setUp(self):
        """测试设置"""
        self.serializer = protobuf_serialization_manager
        self.test_data = {
            'device_id': 'test_device_001',
            'temperature': 25.5,
            'pressure': 1.2,
            'flow_rate': 15.8,
            'power_consumption': 850.0,
            'timestamp': time.time()
        }
    
    def test_serialization_methods_available(self):
        """测试序列化方法是否可用"""
        methods = [
            ExtendedSerializationMethod.PICKLE,
            ExtendedSerializationMethod.MSGPACK,
            ExtendedSerializationMethod.MSGPACK_LZ4,
            ExtendedSerializationMethod.PROTOBUF,
            ExtendedSerializationMethod.PROTOBUF_LZ4
        ]
        
        for method in methods:
            with self.subTest(method=method):
                self.assertIsInstance(method, ExtendedSerializationMethod)
                print(f"✓ 序列化方法可用: {method.value}")
    
    def test_basic_serialization(self):
        """测试基础序列化功能"""
        # 测试传统序列化方法（应该工作）
        methods = [
            ExtendedSerializationMethod.PICKLE,
        ]
        
        # 如果msgpack可用，添加到测试中
        try:
            import msgpack
            methods.extend([
                ExtendedSerializationMethod.MSGPACK,
                ExtendedSerializationMethod.MSGPACK_LZ4
            ])
            print("✓ MessagePack库可用")
        except ImportError:
            print("⚠ MessagePack库不可用，跳过相关测试")
        
        for method in methods:
            with self.subTest(method=method):
                try:
                    # 序列化
                    serialized = self.serializer.serialize(self.test_data, method)
                    self.assertIsInstance(serialized, bytes)
                    self.assertGreater(len(serialized), 0)
                    print(f"✓ 序列化成功: {method.value}, 大小: {len(serialized)} bytes")
                    
                    # 反序列化
                    deserialized = self.serializer.deserialize(serialized, method)
                    self.assertEqual(deserialized['device_id'], self.test_data['device_id'])
                    self.assertAlmostEqual(deserialized['temperature'], self.test_data['temperature'])
                    print(f"✓ 反序列化成功: {method.value}")
                    
                except Exception as e:
                    print(f"✗ 序列化测试失败 {method.value}: {e}")
                    # 对于Protobuf方法，如果库不可用，这是预期的
                    if method in [ExtendedSerializationMethod.PROTOBUF, ExtendedSerializationMethod.PROTOBUF_LZ4]:
                        print(f"  (这是预期的，因为Protobuf库未安装)")
                    else:
                        raise
    
    def test_format_detection(self):
        """测试格式自动检测"""
        # 只测试可用的方法
        test_cases = [
            (ExtendedSerializationMethod.PICKLE, self.test_data),
        ]
        
        # 如果msgpack可用，添加到测试中
        try:
            import msgpack
            test_cases.extend([
                (ExtendedSerializationMethod.MSGPACK, self.test_data),
                (ExtendedSerializationMethod.MSGPACK_LZ4, self.test_data)
            ])
        except ImportError:
            pass
        
        for method, data in test_cases:
            with self.subTest(method=method):
                try:
                    # 序列化
                    serialized = self.serializer.serialize(data, method)
                    
                    # 自动检测并反序列化
                    detected_data = self.serializer.deserialize(serialized)
                    
                    # 验证数据完整性
                    self.assertEqual(detected_data['device_id'], data['device_id'])
                    print(f"✓ 格式检测成功: {method.value}")
                    
                except Exception as e:
                    print(f"✗ 格式检测测试失败 {method.value}: {e}")
                    raise
    
    def test_performance_stats(self):
        """测试性能统计功能"""
        # 执行一些序列化操作
        method = ExtendedSerializationMethod.PICKLE
        
        for _ in range(5):
            serialized = self.serializer.serialize(self.test_data, method)
            self.serializer.deserialize(serialized, method)
        
        # 获取统计信息
        stats = self.serializer.get_stats()
        
        # 验证统计信息
        self.assertIsInstance(stats, dict)
        print(f"✓ 性能统计可用，包含 {len(stats)} 个指标")
        
        # 打印一些关键统计信息
        for key, value in stats.items():
            if 'count' in key or 'time' in key:
                print(f"  {key}: {value}")

class TestProtobufMessages(unittest.TestCase):
    """测试Protobuf消息功能"""
    
    def setUp(self):
        """测试设置"""
        try:
            from DBUtil.industrial_control.v1 import common_pb2
            from DBUtil.industrial_control.v1 import shouwei_service_pb2
            from DBUtil.industrial_control.v1 import kongwen_service_pb2
            self.protobuf_available = True
            self.common_pb2 = common_pb2
            self.shouwei_pb2 = shouwei_service_pb2
            self.kongwen_pb2 = kongwen_service_pb2
            print("✓ Protobuf消息模块加载成功")
        except ImportError as e:
            self.protobuf_available = False
            print(f"⚠ Protobuf消息模块不可用: {e}")
    
    def test_protobuf_messages_available(self):
        """测试Protobuf消息是否可用"""
        if not self.protobuf_available:
            print("⚠ 跳过Protobuf消息测试（模块不可用）")
            return
        
        # 测试创建基础消息
        device_info = self.common_pb2.DeviceInfo()
        device_info.device_id = "test_device"
        device_info.device_type = "shouwei_lasu_control"
        
        self.assertEqual(device_info.device_id, "test_device")
        self.assertEqual(device_info.device_type, "shouwei_lasu_control")
        print("✓ 基础Protobuf消息创建成功")
    
    def test_shouwei_request_creation(self):
        """测试首尾控制请求创建"""
        if not self.protobuf_available:
            print("⚠ 跳过首尾控制请求测试（模块不可用）")
            return
        
        # 创建首尾控制请求
        request = self.shouwei_pb2.ShouweiPredictionRequest()
        
        # 填充数据
        request.device_info.device_id = "shouwei_001"
        request.sensor_data.temperature = 25.5
        request.sensor_data.pressure = 1.2
        request.control_parameters.target_temperature = 26.0
        request.shouwei_data.lasu_coefficient = 1.25
        
        # 验证数据
        self.assertEqual(request.device_info.device_id, "shouwei_001")
        self.assertAlmostEqual(request.sensor_data.temperature, 25.5)
        self.assertAlmostEqual(request.shouwei_data.lasu_coefficient, 1.25)
        print("✓ 首尾控制请求创建成功")
    
    def test_message_serialization(self):
        """测试消息序列化"""
        if not self.protobuf_available:
            print("⚠ 跳过消息序列化测试（模块不可用）")
            return
        
        # 创建并序列化消息
        request = self.shouwei_pb2.ShouweiPredictionRequest()
        request.device_info.device_id = "test_device"
        request.sensor_data.temperature = 25.5
        
        # 序列化
        serialized = request.SerializeToString()
        self.assertIsInstance(serialized, bytes)
        self.assertGreater(len(serialized), 0)
        print(f"✓ 消息序列化成功，大小: {len(serialized)} bytes")
        
        # 反序列化
        new_request = self.shouwei_pb2.ShouweiPredictionRequest()
        new_request.ParseFromString(serialized)
        
        # 验证数据
        self.assertEqual(new_request.device_info.device_id, "test_device")
        self.assertAlmostEqual(new_request.sensor_data.temperature, 25.5)
        print("✓ 消息反序列化成功")

def run_performance_comparison():
    """运行性能对比测试"""
    print("\n=== 序列化性能对比测试 ===")
    
    # 测试数据
    test_data = {
        'device_id': 'performance_test_device',
        'sensor_data': {
            'temperature': 25.5,
            'pressure': 1.2,
            'flow_rate': 15.8,
            'power_consumption': 850.0,
            'humidity': 65.2,
            'vibration': 0.05
        },
        'control_parameters': {
            'target_temperature': 26.0,
            'max_power_limit': 1000,
            'control_precision': 0.1,
            'emergency_stop': False
        },
        'metadata': {
            'timestamp': time.time(),
            'version': 'v1.0',
            'accuracy': 0.95
        }
    }
    
    # 测试可用的方法
    methods = [ExtendedSerializationMethod.PICKLE]
    
    # 检查msgpack可用性
    try:
        import msgpack
        methods.extend([
            ExtendedSerializationMethod.MSGPACK,
            ExtendedSerializationMethod.MSGPACK_LZ4
        ])
        print("✓ MessagePack库可用，将包含在性能测试中")
    except ImportError:
        print("⚠ MessagePack库不可用，仅测试Pickle")
    
    iterations = 1000
    results = {}
    
    print(f"\n开始性能测试 ({iterations} 次迭代)...")
    
    for method in methods:
        try:
            print(f"测试 {method.value}...")
            
            # 序列化性能测试
            start_time = time.time()
            for _ in range(iterations):
                serialized = protobuf_serialization_manager.serialize(test_data, method)
            serialize_time = time.time() - start_time
            
            # 反序列化性能测试
            start_time = time.time()
            for _ in range(iterations):
                deserialized = protobuf_serialization_manager.deserialize(serialized, method)
            deserialize_time = time.time() - start_time
            
            # 数据大小
            data_size = len(serialized)
            
            results[method.value] = {
                'serialize_time': serialize_time,
                'deserialize_time': deserialize_time,
                'total_time': serialize_time + deserialize_time,
                'data_size': data_size,
                'serialize_ops_per_sec': iterations / serialize_time,
                'deserialize_ops_per_sec': iterations / deserialize_time
            }
            
        except Exception as e:
            print(f"✗ 性能测试失败 {method.value}: {e}")
            results[method.value] = {'error': str(e)}
    
    # 输出结果
    print(f"\n性能测试结果:")
    print("-" * 80)
    print(f"{'方法':<15} {'序列化时间':<12} {'反序列化时间':<14} {'数据大小':<10} {'总时间':<10}")
    print("-" * 80)
    
    for method, result in results.items():
        if 'error' not in result:
            print(f"{method:<15} {result['serialize_time']:<12.3f} "
                  f"{result['deserialize_time']:<14.3f} {result['data_size']:<10} "
                  f"{result['total_time']:<10.3f}")
        else:
            print(f"{method:<15} ERROR: {result['error']}")
    
    # 计算改进幅度
    if len(results) > 1:
        pickle_result = results.get('pickle')
        if pickle_result and 'error' not in pickle_result:
            print(f"\n相对于Pickle的改进:")
            print("-" * 50)
            for method, result in results.items():
                if method != 'pickle' and 'error' not in result:
                    size_improvement = (pickle_result['data_size'] - result['data_size']) / pickle_result['data_size'] * 100
                    time_improvement = (pickle_result['total_time'] - result['total_time']) / pickle_result['total_time'] * 100
                    print(f"{method:<15} 大小: {size_improvement:+6.1f}%, 时间: {time_improvement:+6.1f}%")
    
    return results

if __name__ == '__main__':
    print("=== Protobuf基础功能测试 ===")
    
    # 运行单元测试
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行性能对比测试
    run_performance_comparison()
    
    print("\n=== 测试完成 ===")
    print("注意：要获得完整的Protobuf功能，请安装以下依赖：")
    print("  pip install protobuf>=4.21.0 grpcio>=1.50.0")
    print("  pip install msgpack>=1.0.0 lz4>=3.1.0")
