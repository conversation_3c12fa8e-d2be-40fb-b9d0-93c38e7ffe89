syntax = "proto3";

package industrial_control.v1;

import "industrial_control/v1/common.proto";
import "google/protobuf/timestamp.proto";

// 首尾控制预测请求
message ShouweiPredictionRequest {
    DeviceInfo device_info = 1;
    SensorData sensor_data = 2;
    ControlParameters control_parameters = 3;
    
    // 首尾控制特有参数
    ShouweiSpecificData shouwei_data = 4;
}

// 首尾控制特有数据
message ShouweiSpecificData {
    double lasu_coefficient = 1;     // 拉苏系数
    double baseline_power = 2;       // 基线功率
    repeated double weight_corrections = 3; // 权重修正
    PowerDistribution power_distribution = 4;
}

// 功率分配
message PowerDistribution {
    double main_ratio = 1;           // 主功率比例
    double vice_ratio = 2;           // 副功率比例
    double reserve_ratio = 3;        // 备用功率比例
}

// 首尾控制预测响应
message ShouweiPredictionResponse {
    bool success = 1;
    string error_message = 2;
    PredictionResult prediction = 3;
    
    // 首尾控制特有结果
    ShouweiSpecificResult shouwei_result = 4;
}

// 首尾控制特有结果
message ShouweiSpecificResult {
    PowerDistribution optimized_distribution = 1;
    double lasu_efficiency = 2;
    repeated double adjusted_weights = 3;
    double stability_index = 4;      // 稳定性指数
}

// 首尾控制服务
service ShouweiControlService {
    rpc Predict(ShouweiPredictionRequest) returns (ShouweiPredictionResponse);
    rpc GetModelStatus(DeviceInfo) returns (ModelStatusResponse);
    rpc UpdateModel(UpdateModelRequest) returns (UpdateModelResponse);
}

// 模型状态响应
message ModelStatusResponse {
    bool model_available = 1;
    string model_version = 2;
    google.protobuf.Timestamp last_update = 3;
    int32 prediction_count = 4;
    double average_accuracy = 5;
}

// 更新模型请求
message UpdateModelRequest {
    DeviceInfo device_info = 1;
    bytes model_data = 2;            // 序列化的模型数据
    string model_version = 3;
    bool force_update = 4;
}

// 更新模型响应
message UpdateModelResponse {
    bool success = 1;
    string error_message = 2;
    string new_model_version = 3;
}
