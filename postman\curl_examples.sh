
# Protobuf测试curl命令示例

## 1. 首尾控制 - JSON请求
curl -X POST http://localhost:5000/api/v1/shouwei/predict \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d @D:\code\yongxiang\bigdata_framework\pull-crystal-bigdata-0714\postman/shouwei_request.json

## 2. 首尾控制 - Protobuf请求
curl -X POST http://localhost:5000/api/v1/shouwei/predict \
  -H "Content-Type: application/x-protobuf" \
  -H "Accept: application/x-protobuf" \
  --data-binary @D:\code\yongxiang\bigdata_framework\pull-crystal-bigdata-0714\postman/shouwei_request.bin

## 3. 控温 - JSON请求
curl -X POST http://localhost:5000/api/v1/kongwen/predict \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d @D:\code\yongxiang\bigdata_framework\pull-crystal-bigdata-0714\postman/kongwen_request.json

## 4. 控温 - Protobuf请求
curl -X POST http://localhost:5000/api/v1/kongwen/predict \
  -H "Content-Type: application/x-protobuf" \
  -H "Accept: application/x-protobuf" \
  --data-binary @D:\code\yongxiang\bigdata_framework\pull-crystal-bigdata-0714\postman/kongwen_request.bin

## 5. 性能对比测试
# 测量响应时间和数据大小
curl -X POST http://localhost:5000/api/v1/shouwei/predict \
  -H "Content-Type: application/json" \
  -w "Time: %{time_total}s, Size: %{size_download} bytes\n" \
  -d @D:\code\yongxiang\bigdata_framework\pull-crystal-bigdata-0714\postman/shouwei_request.json

curl -X POST http://localhost:5000/api/v1/shouwei/predict \
  -H "Content-Type: application/x-protobuf" \
  -w "Time: %{time_total}s, Size: %{size_download} bytes\n" \
  --data-binary @D:\code\yongxiang\bigdata_framework\pull-crystal-bigdata-0714\postman/shouwei_request.bin
