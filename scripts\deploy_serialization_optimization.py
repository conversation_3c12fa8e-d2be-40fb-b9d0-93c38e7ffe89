#!/usr/bin/env python3
"""
序列化优化部署脚本
自动化部署MessagePack + LZ4优化方案
"""

import os
import sys
import subprocess
import json
import time
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SerializationOptimizationDeployer:
    """序列化优化部署器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.backup_dir = self.project_root / "backups" / f"serialization_backup_{int(time.time())}"
        self.requirements_file = self.project_root / "requirements_optimization.txt"
        
    def check_prerequisites(self) -> bool:
        """检查部署前提条件"""
        logger.info("检查部署前提条件...")
        
        # 检查Python版本
        if sys.version_info < (3.7, 0):
            logger.error("需要Python 3.7或更高版本")
            return False
        
        # 检查项目结构
        required_files = [
            "DBUtil/Redis.py",
            "DBUtil/share.py",
            "main.py"
        ]
        
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                logger.error(f"缺少必要文件: {file_path}")
                return False
        
        logger.info("前提条件检查通过")
        return True
    
    def create_backup(self) -> bool:
        """创建备份"""
        logger.info("创建代码备份...")
        
        try:
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            
            # 备份关键文件
            backup_files = [
                "DBUtil/Redis.py",
                "DBUtil/share.py",
                "main.py",
                "DBUtil/config.json"
            ]
            
            for file_path in backup_files:
                src = self.project_root / file_path
                if src.exists():
                    dst = self.backup_dir / file_path
                    dst.parent.mkdir(parents=True, exist_ok=True)
                    
                    import shutil
                    shutil.copy2(src, dst)
                    logger.info(f"备份文件: {file_path}")
            
            logger.info(f"备份完成: {self.backup_dir}")
            return True
            
        except Exception as e:
            logger.error(f"备份失败: {e}")
            return False
    
    def install_dependencies(self) -> bool:
        """安装依赖包"""
        logger.info("安装优化依赖包...")
        
        try:
            # 检查requirements文件是否存在
            if not self.requirements_file.exists():
                logger.error(f"依赖文件不存在: {self.requirements_file}")
                return False
            
            # 安装依赖
            cmd = [sys.executable, "-m", "pip", "install", "-r", str(self.requirements_file)]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("依赖包安装成功")
                return True
            else:
                logger.error(f"依赖包安装失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"安装依赖包时出错: {e}")
            return False
    
    def verify_installation(self) -> bool:
        """验证安装"""
        logger.info("验证优化库安装...")
        
        try:
            import msgpack
            import lz4.frame
            logger.info("优化库验证成功")
            return True
        except ImportError as e:
            logger.error(f"优化库验证失败: {e}")
            return False
    
    def run_tests(self) -> bool:
        """运行测试"""
        logger.info("运行序列化优化测试...")
        
        try:
            test_file = self.project_root / "tests" / "test_serialization_optimization.py"
            if not test_file.exists():
                logger.warning("测试文件不存在，跳过测试")
                return True
            
            # 运行测试
            cmd = [sys.executable, str(test_file)]
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=str(self.project_root))
            
            if result.returncode == 0:
                logger.info("测试通过")
                print(result.stdout)
                return True
            else:
                logger.error(f"测试失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"运行测试时出错: {e}")
            return False
    
    def update_configuration(self) -> bool:
        """更新配置"""
        logger.info("更新系统配置...")
        
        try:
            config_file = self.project_root / "DBUtil" / "config.json"
            
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 添加序列化配置
                if 'serialization' not in config:
                    config['serialization'] = {
                        'method': 'msgpack_lz4',
                        'enable_fallback': True,
                        'enable_stats': True
                    }
                
                # 备份原配置
                backup_config = self.backup_dir / "config.json.backup"
                with open(backup_config, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                
                # 写入新配置
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                
                logger.info("配置更新完成")
            
            return True
            
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return False
    
    def deploy(self) -> bool:
        """执行完整部署"""
        logger.info("开始序列化优化部署...")
        
        steps = [
            ("检查前提条件", self.check_prerequisites),
            ("创建备份", self.create_backup),
            ("安装依赖", self.install_dependencies),
            ("验证安装", self.verify_installation),
            ("运行测试", self.run_tests),
            ("更新配置", self.update_configuration)
        ]
        
        for step_name, step_func in steps:
            logger.info(f"执行步骤: {step_name}")
            if not step_func():
                logger.error(f"步骤失败: {step_name}")
                return False
        
        logger.info("序列化优化部署完成!")
        logger.info(f"备份位置: {self.backup_dir}")
        return True
    
    def rollback(self) -> bool:
        """回滚到备份版本"""
        logger.info("开始回滚...")
        
        try:
            if not self.backup_dir.exists():
                logger.error("备份目录不存在，无法回滚")
                return False
            
            # 恢复备份文件
            import shutil
            for backup_file in self.backup_dir.rglob("*"):
                if backup_file.is_file():
                    relative_path = backup_file.relative_to(self.backup_dir)
                    target_file = self.project_root / relative_path
                    
                    target_file.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(backup_file, target_file)
                    logger.info(f"恢复文件: {relative_path}")
            
            logger.info("回滚完成")
            return True
            
        except Exception as e:
            logger.error(f"回滚失败: {e}")
            return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="序列化优化部署工具")
    parser.add_argument("--project-root", default=".", help="项目根目录")
    parser.add_argument("--rollback", action="store_true", help="回滚到备份版本")
    parser.add_argument("--backup-dir", help="指定备份目录（用于回滚）")
    
    args = parser.parse_args()
    
    deployer = SerializationOptimizationDeployer(args.project_root)
    
    if args.rollback:
        if args.backup_dir:
            deployer.backup_dir = Path(args.backup_dir)
        success = deployer.rollback()
    else:
        success = deployer.deploy()
    
    if success:
        logger.info("操作成功完成")
        sys.exit(0)
    else:
        logger.error("操作失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
