#!/usr/bin/env python3
"""
Redis连接池监控脚本
提供连接池状态监控和性能分析
"""

import sys
import os
import time
import json
import argparse
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from DBUtil.redis_pool_manager import redis_pool_manager, initialize_redis_pool

class RedisPoolMonitor:
    """Redis连接池监控器"""
    
    def __init__(self):
        self.monitoring = False
    
    def start_monitoring(self, interval: int = 10, duration: int = 300):
        """开始监控"""
        print(f"开始监控Redis连接池，间隔: {interval}秒，持续: {duration}秒")
        print("=" * 80)
        
        self.monitoring = True
        start_time = time.time()
        
        while self.monitoring and (time.time() - start_time) < duration:
            self._print_status()
            time.sleep(interval)
        
        print("\n监控结束")
    
    def _print_status(self):
        """打印当前状态"""
        if not redis_pool_manager:
            print("Redis连接池管理器未初始化")
            return
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"\n[{timestamp}] Redis连接池状态:")
        print("-" * 60)
        
        # 获取统计信息
        stats = redis_pool_manager.get_stats()
        print(f"活跃连接池数: {stats['active_pools']}")
        print(f"活跃客户端数: {stats['active_clients']}")
        print(f"总活跃连接: {stats['total_active_connections']}")
        print(f"总可用连接: {stats['total_available_connections']}")
        print(f"连接利用率: {stats['connection_utilization']:.2%}")
        print(f"连接池命中: {stats['pool_hits']}")
        print(f"健康检查次数: {stats['health_checks']}")
        print(f"健康检查失败: {stats['health_check_failures']}")
        
        # 获取各个连接池详细信息
        pools_info = redis_pool_manager.get_all_pools_info()
        if pools_info:
            print("\n各连接池详情:")
            for db, info in pools_info.items():
                print(f"  DB {db}: 最大={info['max_connections']}, "
                      f"已创建={info['created_connections']}, "
                      f"可用={info['available_connections']}, "
                      f"使用中={info['in_use_connections']}")
    
    def health_check(self):
        """执行健康检查"""
        if not redis_pool_manager:
            print("Redis连接池管理器未初始化")
            return
        
        print("执行Redis连接池健康检查...")
        print("=" * 50)
        
        health_results = redis_pool_manager.health_check()
        
        for db, result in health_results.items():
            status = result['status']
            if status == 'healthy':
                response_time = result['response_time_ms']
                print(f"DB {db}: ✓ 健康 (响应时间: {response_time:.2f}ms)")
            else:
                error = result['error']
                print(f"DB {db}: ✗ 不健康 (错误: {error})")
        
        # 显示连接池信息
        print("\n连接池详情:")
        for db, result in health_results.items():
            pool_info = result['pool_info']
            if 'error' not in pool_info:
                print(f"  DB {db}: {pool_info}")
    
    def performance_test(self, operations: int = 1000, concurrency: int = 10):
        """性能测试"""
        if not redis_pool_manager:
            print("Redis连接池管理器未初始化")
            return
        
        import threading
        import queue
        
        print(f"开始性能测试: {operations}次操作, {concurrency}个并发")
        print("=" * 60)
        
        results_queue = queue.Queue()
        
        def worker():
            client = redis_pool_manager.get_client()
            start_time = time.time()
            errors = 0
            
            for i in range(operations // concurrency):
                try:
                    # 执行简单的Redis操作
                    client.set(f"test_key_{threading.current_thread().ident}_{i}", f"value_{i}")
                    client.get(f"test_key_{threading.current_thread().ident}_{i}")
                except Exception as e:
                    errors += 1
            
            end_time = time.time()
            results_queue.put({
                'duration': end_time - start_time,
                'operations': operations // concurrency,
                'errors': errors
            })
        
        # 启动工作线程
        threads = []
        start_time = time.time()
        
        for _ in range(concurrency):
            thread = threading.Thread(target=worker)
            thread.start()
            threads.append(thread)
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        # 收集结果
        total_operations = 0
        total_errors = 0
        
        while not results_queue.empty():
            result = results_queue.get()
            total_operations += result['operations']
            total_errors += result['errors']
        
        # 显示结果
        print(f"总操作数: {total_operations}")
        print(f"总耗时: {total_time:.2f}秒")
        print(f"QPS: {total_operations / total_time:.2f}")
        print(f"错误数: {total_errors}")
        print(f"错误率: {total_errors / total_operations:.2%}")
        
        # 清理测试数据
        try:
            client = redis_pool_manager.get_client()
            for i in range(concurrency):
                pattern = f"test_key_{i}_*"
                keys = client.keys(pattern)
                if keys:
                    client.delete(*keys)
            print("测试数据已清理")
        except Exception as e:
            print(f"清理测试数据失败: {e}")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Redis连接池监控工具")
    parser.add_argument("--config", default="DBUtil/config.json", help="配置文件路径")
    parser.add_argument("--monitor", action="store_true", help="启动监控模式")
    parser.add_argument("--interval", type=int, default=10, help="监控间隔（秒）")
    parser.add_argument("--duration", type=int, default=300, help="监控持续时间（秒）")
    parser.add_argument("--health-check", action="store_true", help="执行健康检查")
    parser.add_argument("--performance-test", action="store_true", help="执行性能测试")
    parser.add_argument("--operations", type=int, default=1000, help="性能测试操作数")
    parser.add_argument("--concurrency", type=int, default=10, help="性能测试并发数")
    
    args = parser.parse_args()
    
    # 初始化Redis连接池
    try:
        with open(args.config, 'r') as f:
            config = json.load(f)
        
        redis_config = config.get("redis", {})
        redis_config['max_connections'] = 50
        redis_config['max_connections_per_pool'] = 50
        
        initialize_redis_pool(redis_config)
        print("Redis连接池初始化成功")
    except Exception as e:
        print(f"初始化失败: {e}")
        sys.exit(1)
    
    monitor = RedisPoolMonitor()
    
    try:
        if args.monitor:
            monitor.start_monitoring(args.interval, args.duration)
        elif args.health_check:
            monitor.health_check()
        elif args.performance_test:
            monitor.performance_test(args.operations, args.concurrency)
        else:
            # 默认显示当前状态
            monitor._print_status()
    
    except KeyboardInterrupt:
        print("\n用户中断")
        monitor.stop_monitoring()
    except Exception as e:
        print(f"执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
