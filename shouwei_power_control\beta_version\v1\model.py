import yaml
import math
import pickle
import numpy as np
from collections import deque


class ShouweiGonglvCorrectionModel():
    @classmethod
    def from_path(cls, keycurves_path, config_path):
        m = cls()
        m.load_config(config_path)
        m.valid = m.load_model(keycurves_path)
        if m.valid:
            m.enable_corr()
            return m

    def load_config(self, config_path):
        with open(config_path) as f:
            config = yaml.load(f, Loader=yaml.FullLoader)['gonglv']
        self.enter_power_step = config['enter_step']['power']
        self.power_adjust_step = config['power_adjust_step']
        self.power_adjust_limit = config['power_adjust_limit']
        self.wait_length = config['wait_length']
        self.diameter_smooth_window = config['diameter_smooth_window']
        self.diameter_adjust_ratio = config['diameter_adjust_ratio']

    def load_model(self, keycurves_path):
        with open(keycurves_path, 'rb') as f:
            self.keycurves = pickle.load(f)
        return len(self.keycurves) != 0

    def setup(self, product_type, field_size, device_id, power, dengjing_diameter, dengjing_weight, target_dia, target_power, config, weight_bin_path_11, weight_bin_path_12, dengjing_after_100_length,
              dengjing_after_100_grow_lasu, dengjing_after_100_target_lasu, weight_bin_path_11_rate,
              weight_bin_path_105,weight_bin_path_105_rate):
        update_keys = ['power_adjust_step', 'power_adjust_limit', 'wait_length', 'diameter_smooth_window', 'diameter_adjust_ratio']
        for key in update_keys:
            if key in config and config[key] is not None:
                setattr(self, key, config[key])
        vs = self.keycurves[min(self.keycurves.keys(), key=lambda v: abs(float(v) - float(product_type)))]
        vs = vs.get(device_id, [v for d in vs.values() for v in d])
        vs1 = [v for v in vs if float(v['field_size']) - float(field_size) == 0]
        v = min(vs1, key=lambda v: abs(v['power'][0] - power))
        self.power = (v['power'] + (power - v['power'][0])) if len(target_power) == 0 else np.array(target_power)
        self.diameters = v['diameter'] if len(target_dia) == 0 else np.array(target_dia)
        self.corr, self.before_diameters, self.real_power, self.last_adjust = 0, [], [], 0
        self.prev_r, self.prev_l, self.prev_m = dengjing_diameter / 2, 0, dengjing_weight

        self.product_type = product_type
        with open(weight_bin_path_11, 'rb') as f:
            self.weight_bin_dict_11 = pickle.load(f)
        with open(weight_bin_path_12, 'rb') as f:
            self.weight_bin_dict_12 = pickle.load(f)
        with open(weight_bin_path_105, 'rb') as f:
            self.weight_bin_dict_105 = pickle.load(f)
        if product_type == '11':
            self.weight_bin_dict = self.weight_bin_dict_11
        elif product_type == '10.5':
            self.weight_bin_dict = self.weight_bin_dict_105
        else:
            self.weight_bin_dict = self.weight_bin_dict_12
        self.history_len_weight = {}
        self.offset = [0, 0.1, -0.1, 0.2, -0.2]
        self.history_weight_piancha = {}

        self.pre_last_adjust = 0
        self.previous_power_correction = 0.4
        self.large_xishu = 1.2
        self.small_xishu = 1
        self.corr1 = 0  # 等径后50长拉速修正量
        self.corr1_all = 0
        self.corr2 = 0  # 重量变化率修正量
        self.corr3 = 0  # 大区间重量变化修正量
        self.corr3_all = 0  # 大区间重量变化修正量
        self.corr3_adjust_50 = 0
        self.corr3_adjust_100 = 0
        self.corr3_flag_50 = 0
        self.corr3_flag_100 = 0
        self.integral = 0
        self.slope = 0

        # 对齐长度，计算等径后50长生长拉速-目标拉速的积分值
        min_len = min(len(dengjing_after_100_grow_lasu), len(dengjing_after_100_target_lasu), len(dengjing_after_100_length))
        dengjing_after_100_grow_lasu = dengjing_after_100_grow_lasu[:min_len]
        dengjing_after_100_target_lasu = dengjing_after_100_target_lasu[:min_len]
        dengjing_after_100_length = dengjing_after_100_length[:min_len]

        # 提取后50长
        dengjing_length = dengjing_after_100_length[-1]
        dengjing_after_50 = dengjing_length - 50
        closest_index = min(range(len(dengjing_after_100_length)), key=lambda i: abs(dengjing_after_100_length[i] - dengjing_after_50))
        dengjing_after_50_length = dengjing_after_100_length[closest_index:]
        dengjing_after_50_grow_lasu = dengjing_after_100_grow_lasu[closest_index:]
        dengjing_after_50_target_lasu = dengjing_after_100_target_lasu[closest_index:]

        delta_speed = [g - t for g, t in zip(dengjing_after_50_grow_lasu, dengjing_after_50_target_lasu)]
        # 使用梯形法则进行数值积分
        self.integral = np.trapz(delta_speed, x=dengjing_after_50_length)

        # 计算变化斜率
        dengjing_after_10 = dengjing_length - 10  # 提取后10长数据
        closest_index = min(range(len(dengjing_after_100_length)), key=lambda i: abs(dengjing_after_100_length[i] - dengjing_after_10))
        dengjing_after_10_length = dengjing_after_100_length[closest_index:]
        dengjing_after_10_grow_lasu = dengjing_after_100_grow_lasu[closest_index:]

        # 提取前后三个点，生长拉速，和单位长度
        grow_lasu_start_3_points = dengjing_after_10_grow_lasu[:3]  # 前 3 个点
        grow_lasu_end_3_points = dengjing_after_10_grow_lasu[-3:]  # 后 3 个点
        unit_length_start_3_points = dengjing_after_10_length[:3]  # 前 3 个点
        unit_length_end_3_points = dengjing_after_10_length[-3:]  # 后 3 个点

        avg_growth_speed_start = round(sum(grow_lasu_start_3_points) / len(grow_lasu_start_3_points), 1)
        avg_growth_speed_end = round(sum(grow_lasu_end_3_points) / len(grow_lasu_end_3_points), 1)
        avg_unit_length_start = round(sum(unit_length_start_3_points) / len(unit_length_start_3_points), 1)
        avg_unit_length_end = round(sum(unit_length_end_3_points) / len(unit_length_end_3_points), 1)

        # 生长拉速变化斜率
        self.slope = (avg_growth_speed_end - avg_growth_speed_start) / (avg_unit_length_end - avg_unit_length_start)

        # 判断情况升功率
        if self.integral > 0 and self.slope <= 0:
            self.corr1_all = self.previous_power_correction * self.small_xishu
        if self.integral > 0 and self.slope >= 0:
            self.corr1_all = self.previous_power_correction * self.large_xishu
        if self.integral < 0 and self.slope <= 0:
            self.corr1_all = 0
        if self.integral < 0 and self.slope >= 0:
            self.corr1_all = self.previous_power_correction * self.small_xishu
        if self.integral == 0:
            self.corr1_all = 0

        # 每一长平均修正量
        self.corr1 = self.corr1_all / 10

        self.latest_weights = deque(maxlen=3)
        with open(weight_bin_path_11_rate, 'rb') as f:
            self.weight_bin_dict_11_rate = pickle.load(f)
        with open(weight_bin_path_105_rate, 'rb') as f:
            self.weight_bin_dict_105_rate = pickle.load(f)
        if self.product_type == '11':
            self.large_interval_weight_change = [6.6, 10.8]
        else:
            self.large_interval_weight_change = [5.5, 8.9]
        self.real_time_50_weight_change = 0
        self.real_time_100_weight_change = 0
        self.power_adjust_limit_11 = 2
        self.power_adjust_limit_12 = 2
        if self.product_type == '11':
            self.weight_bin_dict_rate = self.weight_bin_dict_11_rate
        else:
            self.weight_bin_dict_rate = self.weight_bin_dict_105_rate
    def baseline(self):
        return {'shape_baseline': self.diameters.tolist(), 'power_baseline': self.power.tolist()}

    def finish(self,end_code):
        return self.corr

    def clear_corr(self):
        self.corr, self.last_adjust = 0, None

    def disable_corr(self):
        self.do_corr = False

    def enable_corr(self):
        self.do_corr = True

    def disable_history_corr(self):
        self.do_history_corr = False

    def enable_history_corr(self):
        self.do_history_corr = True

    def predict(self, l, weight):

        if (not self.do_corr) or l >= self.power.size:
            power = self.power[min(int(l), self.power.size - 1)] + (self.corr if self.do_corr else 0)
            return power

        self.history_len_weight[l] = weight
        # 用于存储当前最新的三个重量值
        self.latest_weights.append(weight)
        if self.product_type == '11' or self.product_type == '10.5':
            l = int(l)
            # 在前10长修正功率
            if l <= self.wait_length:
                if l - self.pre_last_adjust == 1:
                    self.corr += self.corr1
                    self.pre_last_adjust = l

            if l - self.last_adjust >= self.wait_length:

                matching_weights = [0, 0, 0]
                data_list = [(key, value) for key, value in self.history_len_weight.items()]
                for increment in self.offset:
                    adjusted_length = l - 10 + increment
                    # 遍历列表查找匹配的单位长度
                    for i in range(2, len(data_list)):  # 从索引2开始，确保有前两个索引
                        if data_list[i][0] == adjusted_length:
                            # 找到匹配的单位长度，提取前两个索引的晶体重量值和当前匹配的晶体重量值
                            matching_weights = [data_list[i - 2][1], data_list[i - 1][1], data_list[i][1]]
                            break
                    if matching_weights:  # 如果找到了匹配的值，跳出增量调整循环
                        break
                else:
                    print("没有匹配到目标单位长度。")

                # 剔除异常值，相邻之间的两个数差值大于0.2
                def remove_outliers(lst, threshold=0.2):
                    # 保留第一个元素
                    filtered_lst = [lst[0]]
                    # 从第二个元素开始检查
                    for i in range(1, len(lst)):
                        # 如果当前元素与前一个元素的差值大于阈值，则跳过当前元素
                        if abs(lst[i] - lst[i - 1]) > threshold:
                            continue
                        # 否则，保留当前元素
                        filtered_lst.append(lst[i])
                    return filtered_lst

                matching_weights = remove_outliers(matching_weights)

                pre_10_len_weight = round(sum(matching_weights) / len(matching_weights), 2)

                if pre_10_len_weight == 0:  # 预防找不到前10长重量的情况
                    pre_10_len_weight = weight

                # 计算当前重量的均值
                latest_weights = list(self.latest_weights)
                latest_weights = remove_outliers(latest_weights)
                real_weight = round(sum(latest_weights) / len(latest_weights), 2)

                real_weight_piancha = round(real_weight - pre_10_len_weight, 1)
                standard_weight_piancha = self.weight_bin_dict[l]
                weight_piancha = round(real_weight_piancha - standard_weight_piancha, 1)

                self.history_weight_piancha[l] = real_weight_piancha
                if weight_piancha > 0.1:
                    self.corr += self.power_adjust_step * 2
                elif weight_piancha == 0.1:
                    self.corr += self.power_adjust_step
                elif weight_piancha == 0 or weight_piancha < 0:
                    self.corr += 0

                # 根据大区间重量变化率去修正功率
                if l == 50:
                    standard_large_interval_weight_change = self.large_interval_weight_change[0]
                    for i in [50, 40, 30, 20, 10]:
                        self.real_time_50_weight_change += self.history_weight_piancha[i]
                    if round(self.real_time_50_weight_change - standard_large_interval_weight_change, 1) > 0.2:
                        self.corr3_all = self.power_adjust_step * 2
                        self.corr3 = self.corr3_all / 5
                        self.corr += self.corr3
                        self.corr3_adjust_50 = l
                        self.corr3_flag_50 = 1

                # 在后50长均匀修正
                if l - self.corr3_adjust_50 <= 40 and self.corr3_flag_50 == 1:
                    self.corr += self.corr3

                if l == 100:
                    standard_large_interval_weight_change = self.large_interval_weight_change[1]
                    for i in [100, 90, 80, 70, 60]:
                        self.real_time_100_weight_change += self.history_weight_piancha[i]
                    self.real_time_100_weight_change += self.real_time_50_weight_change
                    if round(self.real_time_100_weight_change - standard_large_interval_weight_change, 1) > 0.2:
                        self.corr3_all = self.power_adjust_step * 2
                        self.corr3 = self.corr3_all / 5
                        self.corr += self.corr3
                        self.corr3_adjust_100 = l
                        self.corr3_flag_100 = 1

                # 在后50长均匀修正
                if l - self.corr3_adjust_100 <= 40 and self.corr3_flag_100 == 1:
                    self.corr += self.corr3

                # 在大于150长后，将标志位置0
                if l >= 150:
                    self.corr3_flag_50 = 0
                    self.corr3_flag_100 = 0
                    self.corr3 = 0

                # 根据重量变化率去修正功率
                if l >= 20 and self.corr3_flag_50 == 0 and self.corr3_flag_100 == 0:
                    standard_weight_rate = self.weight_bin_dict_rate[l]
                    try:
                        real_time_rate = self.history_weight_piancha[l] / self.history_weight_piancha[l - 10]
                        if real_time_rate > standard_weight_rate:
                            self.corr2 = 0.1
                            self.corr += self.corr2
                    except:
                        print("存在错误")

                self.last_adjust = l
                self.corr = np.clip(self.corr, -self.power_adjust_limit_11, self.power_adjust_limit_11)

        if self.product_type == '12':
            l = int(l)
            if l <= self.wait_length:
                if l - self.pre_last_adjust == 1:
                    self.corr += self.corr1
                    self.pre_last_adjust = l
            if l - self.last_adjust >= self.wait_length:

                matching_weights = [0, 0, 0]
                data_list = [(key, value) for key, value in self.history_len_weight.items()]
                for increment in self.offset:
                    adjusted_length = l - 10 + increment
                    # 遍历列表查找匹配的单位长度
                    for i in range(2, len(data_list)):  # 从索引2开始，确保有前两个索引
                        if data_list[i][0] == adjusted_length:
                            # 找到匹配的单位长度，提取前两个索引的晶体重量值和当前匹配的晶体重量值
                            matching_weights = [data_list[i - 2][1], data_list[i - 1][1], data_list[i][1]]
                            break
                    if matching_weights:  # 如果找到了匹配的值，跳出增量调整循环
                        break
                else:
                    print("没有匹配到目标单位长度。")

                # 剔除异常值，相邻之间的两个数差值大于0.2
                def remove_outliers(lst, threshold=0.2):
                    # 保留第一个元素
                    filtered_lst = [lst[0]]
                    # 从第二个元素开始检查
                    for i in range(1, len(lst)):
                        # 如果当前元素与前一个元素的差值大于阈值，则跳过当前元素
                        if abs(lst[i] - lst[i - 1]) > threshold:
                            continue
                        # 否则，保留当前元素
                        filtered_lst.append(lst[i])
                    return filtered_lst

                matching_weights = remove_outliers(matching_weights)

                pre_10_len_weight = round(sum(matching_weights) / len(matching_weights), 2)

                if pre_10_len_weight == 0:  # 预防找不到前15长重量的情况
                    pre_10_len_weight = weight

                # 计算当前重量的均值
                latest_weights = list(self.latest_weights)
                latest_weights = remove_outliers(latest_weights)
                real_weight = round(sum(latest_weights) / len(latest_weights), 2)

                real_weight_piancha = round(real_weight - pre_10_len_weight, 1)
                standard_weight_piancha = self.weight_bin_dict[l]
                weight_piancha = round(real_weight_piancha - standard_weight_piancha, 1)

                self.history_weight_piancha[l] = real_weight_piancha
                if weight_piancha > 0.1:
                    self.corr += self.power_adjust_step * 3
                elif weight_piancha == 0.1:
                    self.corr += self.power_adjust_step * 2
                elif weight_piancha == 0 or weight_piancha < 0:
                    self.corr += 0

                self.last_adjust = l
                self.corr = np.clip(self.corr, -self.power_adjust_limit_12, self.power_adjust_limit_12)

        power = self.power[l] + self.corr

        return power