import json

from dbutils.pooled_db import PooledDB,SharedDBConnection
import threading
import sys
import pymysql

class PsycopgConn:

    _instance_lock = threading.Lock()

    # def __init__(self):
    #     self.init_pool()

    def __new__(cls, *args, **kwargs):
        if not hasattr(cls, '_instance'):
            with PsycopgConn._instance_lock:
                if not hasattr(cls, '_instance'):
                    PsycopgConn._instance = object.__new__(cls)
        return PsycopgConn._instance
		
    def get_pool_conn(self):
        """
        获取连接池连接
        :return: 
        """
        if not self._pool:
            self.init_pool()
        return self._pool.connection()

    def init_pool(self,name):
        """
        初始化连接池
        :return: 
        """
        with open("DBUtil/config.json", 'r') as file:
            json_string = file.read()
        config = json.loads(json_string)
        selected_name = config.get(name,None)
        if selected_name:
            host = selected_name['host']
            port = selected_name['port']
            user = selected_name['user']
            password = selected_name['password']
            database = selected_name['database']
            print(host,port,user,password,database)
        try:
            pool = PooledDB(
                creator=pymysql,  # 使用连接数据库的模块 psycopg2
                maxconnections=20,  # 连接池允许的最大连接数，0 和 None 表示不限制连接数
                mincached=5,       # 初始化时，链接池中至少创建的空闲的链接，0 表示不创建
                maxcached=2,       # 链接池中最多闲置的链接，0 和 None 不限制
                blocking=True,    # 连接池中如果没有可用连接后，是否阻塞等待。True，等待；False，不等待然后报错
                maxusage=None,    # 一个链接最多被重复使用的次数，None 表示无限制
                setsession=[],     # 开始会话前执行的命令列表
                #永祥
                # host='*************',
                # port=13308,
                # user='root',
                # password='jxdq2021',
                # database='yx_bigdata'
                #本地服务器
                host = host,
                port = port,
                user = user,
                password = password,
                database = database
                )
            self._pool = pool
        except:
            print('connect postgresql error')
            self.close_pool()
        # return pool

    def close_pool(self):
        """
        关闭连接池连接
        :return: 
        """
        if self._pool != None:
            self._pool.close()

    @staticmethod
    def SelectSql(conn,sql):
        """
        查询
        :param sql: 
        :return: 
        """
        try:
            # conn = self.get_pool_conn()
            cursor = conn.cursor()
            cursor.execute(sql)
            result = cursor.fetchall()
        except Exception as e:
            print('execute sql {0} is error'.format(sql))
            sys.exit('ERROR: load data from database error caused {0}'.format(str(e)))
        finally:
            cursor.close()
            conn.close()
        return result
    @staticmethod
    def InsertSql(conn,sql):
        """
        插入数据
        :param sql: 
        :return: 
        """
        try:
            # conn = self.get_pool_conn()
            cursor = conn.cursor()
            cursor.execute(sql)
            result = True
        except Exception as e:
            print('ERROR: execute  {0} causes error'.format(sql))
            sys.exit('ERROR: update data from database error caused {0}'.format(str(e)))
        finally:
            cursor.close()
            conn.commit()
            conn.close()
        return result
    @staticmethod
    def UpdateSql(conn,sql):
        """
        更新数据
        :param sql: 
        :return: 
        """
        try:
            # conn = self.get_pool_conn()
            cursor = conn.cursor()
            cursor.execute(sql)
            result = True
        except Exception as e:
            print('ERROR: execute  {0} causes error'.format(sql))
            sys.exit('ERROR: update data from database error caused {0}'.format(str(e)))
        finally:
            cursor.close()
            conn.commit()
            conn.close()
        return result
