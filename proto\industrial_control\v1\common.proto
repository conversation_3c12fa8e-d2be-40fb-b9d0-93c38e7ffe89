syntax = "proto3";

package industrial_control.v1;

option go_package = "github.com/company/industrial-control/proto/v1";
option java_package = "com.company.industrial.control.v1";
option csharp_namespace = "Company.Industrial.Control.V1";

import "google/protobuf/timestamp.proto";

// 设备标识
message DeviceInfo {
    string device_id = 1;
    string device_type = 2;  // "shouwei_lasu_control", "kongwen_power_control"
    string location = 3;
    google.protobuf.Timestamp last_update = 4;
}

// 传感器数据
message SensorData {
    double temperature = 1;      // 温度 (°C)
    double pressure = 2;         // 压力 (bar)
    double flow_rate = 3;        // 流量 (L/min)
    double power_consumption = 4; // 功耗 (W)
    double humidity = 5;         // 湿度 (%)
    double vibration = 6;        // 振动 (mm/s)
    
    // 扩展字段（向后兼容）
    map<string, double> additional_sensors = 10;
}

// 控制参数
message ControlParameters {
    double target_temperature = 1;  // 目标温度
    int32 max_power_limit = 2;      // 最大功率限制
    double control_precision = 3;    // 控制精度
    bool emergency_stop = 4;         // 紧急停止
    
    // 扩展控制参数
    map<string, double> additional_params = 10;
}

// 预测结果
message PredictionResult {
    double main_power = 1;           // 主功率预测
    double vice_power = 2;           // 副功率预测
    double total_power = 3;          // 总功率
    double predicted_temperature = 4; // 预测温度
    double confidence = 5;           // 置信度 [0.0, 1.0]
    double efficiency = 6;           // 效率
    
    // 控制策略
    ControlStrategy control_strategy = 7;
    
    // 元数据
    PredictionMetadata metadata = 8;
}

// 控制策略
message ControlStrategy {
    double main_power_setpoint = 1;
    double vice_power_setpoint = 2;
    int32 control_mode = 3;          // 1=auto, 2=manual, 3=emergency
    repeated ControlAction actions = 4;
}

// 控制动作
message ControlAction {
    string action_type = 1;          // "adjust_power", "change_mode", etc.
    double value = 2;
    google.protobuf.Timestamp execute_time = 3;
    int32 priority = 4;              // 1=high, 2=medium, 3=low
}

// 预测元数据
message PredictionMetadata {
    string model_version = 1;
    google.protobuf.Timestamp prediction_time = 2;
    int32 processing_time_ms = 3;
    bool cache_hit = 4;
    string serialization_method = 5; // "protobuf", "json", "msgpack"
}

// 通用响应
message CommonResponse {
    bool success = 1;
    string error_message = 2;
    int32 error_code = 3;
}
