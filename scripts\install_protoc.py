#!/usr/bin/env python3
"""
protoc自动安装脚本
自动下载和安装Protocol Buffer编译器
"""

import os
import sys
import platform
import subprocess
import urllib.request
import zipfile
import shutil
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProtocInstaller:
    """protoc安装器"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.machine = platform.machine().lower()
        self.protoc_version = "25.1"  # 最新稳定版本
        self.install_dir = Path.home() / ".protoc"
        
    def install(self):
        """执行安装"""
        logger.info("开始安装protoc...")
        
        try:
            # 1. 检查是否已安装
            if self._check_existing_installation():
                logger.info("protoc已安装，无需重复安装")
                return True
            
            # 2. 确定下载URL
            download_url = self._get_download_url()
            if not download_url:
                logger.error("无法确定下载URL")
                return False
            
            # 3. 下载protoc
            if not self._download_protoc(download_url):
                return False
            
            # 4. 解压和安装
            if not self._extract_and_install():
                return False
            
            # 5. 设置环境变量
            if not self._setup_environment():
                return False
            
            # 6. 验证安装
            if not self._verify_installation():
                return False
            
            logger.info("🎉 protoc安装成功！")
            self._print_usage_instructions()
            return True
            
        except Exception as e:
            logger.error(f"安装失败: {e}")
            return False
    
    def _check_existing_installation(self):
        """检查现有安装"""
        try:
            result = subprocess.run(['protoc', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"发现已安装的protoc: {result.stdout.strip()}")
                return True
        except FileNotFoundError:
            pass
        
        return False
    
    def _get_download_url(self):
        """获取下载URL"""
        base_url = f"https://github.com/protocolbuffers/protobuf/releases/download/v{self.protoc_version}"
        
        if self.system == "windows":
            if "64" in self.machine or "amd64" in self.machine:
                filename = f"protoc-{self.protoc_version}-win64.zip"
            else:
                filename = f"protoc-{self.protoc_version}-win32.zip"
        elif self.system == "linux":
            if "64" in self.machine or "amd64" in self.machine:
                filename = f"protoc-{self.protoc_version}-linux-x86_64.zip"
            else:
                filename = f"protoc-{self.protoc_version}-linux-x86_32.zip"
        elif self.system == "darwin":  # macOS
            if "arm" in self.machine or "aarch64" in self.machine:
                filename = f"protoc-{self.protoc_version}-osx-aarch_64.zip"
            else:
                filename = f"protoc-{self.protoc_version}-osx-x86_64.zip"
        else:
            logger.error(f"不支持的操作系统: {self.system}")
            return None
        
        download_url = f"{base_url}/{filename}"
        logger.info(f"下载URL: {download_url}")
        return download_url
    
    def _download_protoc(self, url):
        """下载protoc"""
        try:
            # 创建安装目录
            self.install_dir.mkdir(parents=True, exist_ok=True)
            
            # 下载文件
            zip_path = self.install_dir / "protoc.zip"
            logger.info(f"下载到: {zip_path}")
            
            logger.info("开始下载protoc...")
            urllib.request.urlretrieve(url, zip_path)
            logger.info("下载完成")
            
            return True
            
        except Exception as e:
            logger.error(f"下载失败: {e}")
            return False
    
    def _extract_and_install(self):
        """解压和安装"""
        try:
            zip_path = self.install_dir / "protoc.zip"
            
            logger.info("解压protoc...")
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(self.install_dir)
            
            # 删除zip文件
            zip_path.unlink()
            
            # 设置可执行权限（Linux/macOS）
            if self.system != "windows":
                protoc_bin = self.install_dir / "bin" / "protoc"
                if protoc_bin.exists():
                    os.chmod(protoc_bin, 0o755)
            
            logger.info("解压完成")
            return True
            
        except Exception as e:
            logger.error(f"解压失败: {e}")
            return False
    
    def _setup_environment(self):
        """设置环境变量"""
        try:
            bin_dir = self.install_dir / "bin"
            
            if self.system == "windows":
                # Windows环境变量设置
                logger.info("设置Windows环境变量...")
                
                # 获取当前PATH
                current_path = os.environ.get('PATH', '')
                new_path_entry = str(bin_dir)
                
                if new_path_entry not in current_path:
                    # 临时设置（当前会话）
                    os.environ['PATH'] = f"{new_path_entry};{current_path}"
                    
                    # 提示用户永久设置
                    logger.info("临时环境变量已设置")
                    logger.info("要永久设置，请手动添加到系统PATH:")
                    logger.info(f"  {new_path_entry}")
            else:
                # Linux/macOS环境变量设置
                logger.info("设置Unix环境变量...")
                
                # 添加到当前会话
                current_path = os.environ.get('PATH', '')
                new_path_entry = str(bin_dir)
                
                if new_path_entry not in current_path:
                    os.environ['PATH'] = f"{new_path_entry}:{current_path}"
                    
                    # 提示用户永久设置
                    shell_rc = Path.home() / ".bashrc"
                    if not shell_rc.exists():
                        shell_rc = Path.home() / ".zshrc"
                    
                    logger.info("临时环境变量已设置")
                    logger.info("要永久设置，请添加到shell配置文件:")
                    logger.info(f"  echo 'export PATH=\"{new_path_entry}:$PATH\"' >> {shell_rc}")
            
            return True
            
        except Exception as e:
            logger.error(f"环境变量设置失败: {e}")
            return False
    
    def _verify_installation(self):
        """验证安装"""
        try:
            # 直接调用安装的protoc
            protoc_path = self.install_dir / "bin" / "protoc"
            if self.system == "windows":
                protoc_path = protoc_path.with_suffix(".exe")
            
            if not protoc_path.exists():
                logger.error(f"protoc可执行文件不存在: {protoc_path}")
                return False
            
            # 测试版本
            result = subprocess.run([str(protoc_path), '--version'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"protoc安装验证成功: {result.stdout.strip()}")
                return True
            else:
                logger.error(f"protoc验证失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"验证失败: {e}")
            return False
    
    def _print_usage_instructions(self):
        """打印使用说明"""
        protoc_path = self.install_dir / "bin" / "protoc"
        if self.system == "windows":
            protoc_path = protoc_path.with_suffix(".exe")
        
        print("\n" + "="*60)
        print("🎉 protoc安装完成！")
        print("="*60)
        
        print(f"\n📍 安装位置: {self.install_dir}")
        print(f"📍 可执行文件: {protoc_path}")
        
        print("\n🔧 使用方法:")
        print("1. 编译单个proto文件:")
        print(f"   {protoc_path} --python_out=. your_file.proto")
        
        print("\n2. 编译项目proto文件:")
        print("   python scripts/compile_proto_grpc.py")
        
        print("\n3. 验证安装:")
        print(f"   {protoc_path} --version")
        
        if self.system == "windows":
            print("\n⚠️  Windows用户注意:")
            print("   如果命令行中无法直接使用'protoc'命令，")
            print("   请将以下路径添加到系统PATH环境变量:")
            print(f"   {self.install_dir / 'bin'}")
        
        print("\n" + "="*60)

def main():
    """主函数"""
    print("protoc自动安装工具")
    print("="*40)
    
    installer = ProtocInstaller()
    success = installer.install()
    
    if success:
        print("\n下一步: 编译proto文件")
        print("运行: python scripts/compile_proto_grpc.py")
    else:
        print("\n安装失败，请手动安装protoc:")
        print("Windows: https://github.com/protocolbuffers/protobuf/releases")
        print("macOS: brew install protobuf")
        print("Ubuntu: sudo apt-get install protobuf-compiler")
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
