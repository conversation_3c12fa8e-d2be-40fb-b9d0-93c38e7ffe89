{"info": {"name": "Industrial Control API Tests", "description": "Protocol Buffers集成工业控制系统API测试集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:5000", "type": "string"}, {"key": "api_version", "value": "v1", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/{{api_version}}/health", "host": ["{{base_url}}"], "path": ["api", "{{api_version}}", "health"]}}, "response": []}, {"name": "Shouwei Control - JSON", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"device_info\": {\n    \"device_id\": \"shouwei_device_001\",\n    \"device_type\": \"shouwei_lasu_control\",\n    \"location\": \"production_line_1\"\n  },\n  \"sensor_data\": {\n    \"temperature\": 25.5,\n    \"pressure\": 1.2,\n    \"flow_rate\": 15.8,\n    \"power_consumption\": 850.0,\n    \"humidity\": 65.2,\n    \"vibration\": 0.05,\n    \"additional_sensors\": {\n      \"oil_temperature\": 45.2,\n      \"bearing_temperature\": 38.5\n    }\n  },\n  \"control_parameters\": {\n    \"target_temperature\": 26.0,\n    \"max_power_limit\": 1000,\n    \"control_precision\": 0.1,\n    \"emergency_stop\": false,\n    \"additional_params\": {\n      \"pid_kp\": 1.2,\n      \"pid_ki\": 0.8,\n      \"pid_kd\": 0.3\n    }\n  },\n  \"shouwei_data\": {\n    \"lasu_coefficient\": 1.25,\n    \"baseline_power\": 500.0,\n    \"weight_corrections\": [0.95, 1.02, 0.98, 1.01, 0.99],\n    \"power_distribution\": {\n      \"main_ratio\": 0.7,\n      \"vice_ratio\": 0.25,\n      \"reserve_ratio\": 0.05\n    }\n  },\n  \"timestamp\": 1691234567890,\n  \"request_id\": \"req_shouwei_001\"\n}"}, "url": {"raw": "{{base_url}}/api/{{api_version}}/shouwei/predict", "host": ["{{base_url}}"], "path": ["api", "{{api_version}}", "shouwei", "predict"]}}, "response": []}, {"name": "Kongwen Control - JSON", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"device_info\": {\n    \"device_id\": \"kongwen_device_001\",\n    \"device_type\": \"kongwen_power_control\",\n    \"location\": \"thermal_control_zone_A\"\n  },\n  \"sensor_data\": {\n    \"temperature\": 24.8,\n    \"pressure\": 1.1,\n    \"flow_rate\": 12.5,\n    \"power_consumption\": 750.0,\n    \"humidity\": 58.3,\n    \"vibration\": 0.03,\n    \"additional_sensors\": {\n      \"ambient_temperature\": 20.0,\n      \"surface_temperature\": 28.5\n    }\n  },\n  \"control_parameters\": {\n    \"target_temperature\": 25.5,\n    \"max_power_limit\": 800,\n    \"control_precision\": 0.05,\n    \"emergency_stop\": false,\n    \"additional_params\": {\n      \"heating_mode\": \"gradual\",\n      \"cooling_mode\": \"active\"\n    }\n  },\n  \"kongwen_data\": {\n    \"thermal_capacity\": 1000.0,\n    \"heat_transfer_coefficient\": 10.0,\n    \"ambient_temperature\": 20.0,\n    \"thermal_model\": {\n      \"time_constant\": 120.0,\n      \"gain\": 0.85,\n      \"dead_time\": 5.0,\n      \"pid_parameters\": [1.5, 0.6, 0.2]\n    }\n  },\n  \"timestamp\": 1691234567890,\n  \"request_id\": \"req_kongwen_001\"\n}"}, "url": {"raw": "{{base_url}}/api/{{api_version}}/kongwen/predict", "host": ["{{base_url}}"], "path": ["api", "{{api_version}}", "kong<PERSON>", "predict"]}}, "response": []}, {"name": "Generic Predict - JSON (Backward Compatible)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"device_id\": \"generic_device_001\",\n  \"device_type\": \"shouwei_lasu_control\",\n  \"timestamp\": 1691234567890,\n  \"sensor_data\": {\n    \"temperature\": 25.5,\n    \"pressure\": 1.2,\n    \"flow_rate\": 15.8,\n    \"power_consumption\": 850.0,\n    \"humidity\": 65.2,\n    \"vibration\": 0.05\n  },\n  \"control_parameters\": {\n    \"target_temperature\": 26.0,\n    \"max_power_limit\": 1000,\n    \"control_precision\": 0.1,\n    \"emergency_stop\": false\n  }\n}"}, "url": {"raw": "{{base_url}}/api/{{api_version}}/predict", "host": ["{{base_url}}"], "path": ["api", "{{api_version}}", "predict"]}}, "response": []}, {"name": "Shouwei Control - Protobuf (Binary)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-protobuf"}, {"key": "Accept", "value": "application/x-protobuf"}], "body": {"mode": "file", "file": {"src": "shouwei_request.bin"}}, "url": {"raw": "{{base_url}}/api/{{api_version}}/shouwei/predict", "host": ["{{base_url}}"], "path": ["api", "{{api_version}}", "shouwei", "predict"]}}, "response": []}, {"name": "Performance Test - JSON vs Protobuf", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "X-Performance-Test", "value": "true"}], "body": {"mode": "raw", "raw": "{\n  \"device_info\": {\n    \"device_id\": \"perf_test_device\",\n    \"device_type\": \"shouwei_lasu_control\",\n    \"location\": \"performance_test_zone\"\n  },\n  \"sensor_data\": {\n    \"temperature\": 25.5,\n    \"pressure\": 1.2,\n    \"flow_rate\": 15.8,\n    \"power_consumption\": 850.0,\n    \"humidity\": 65.2,\n    \"vibration\": 0.05\n  },\n  \"control_parameters\": {\n    \"target_temperature\": 26.0,\n    \"max_power_limit\": 1000,\n    \"control_precision\": 0.1,\n    \"emergency_stop\": false\n  },\n  \"shouwei_data\": {\n    \"lasu_coefficient\": 1.25,\n    \"baseline_power\": 500.0,\n    \"weight_corrections\": [0.95, 1.02, 0.98, 1.01, 0.99]\n  }\n}"}, "url": {"raw": "{{base_url}}/api/{{api_version}}/shouwei/predict", "host": ["{{base_url}}"], "path": ["api", "{{api_version}}", "shouwei", "predict"]}}, "response": []}]}