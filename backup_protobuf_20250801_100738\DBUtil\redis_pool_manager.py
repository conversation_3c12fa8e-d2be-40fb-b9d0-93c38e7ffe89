#!/usr/bin/env python3
"""
Redis连接池管理器
提供高性能、高可用的Redis连接管理
"""

import redis
import time
import threading
import logging
from typing import Dict, Any, Optional
from redis.connection import ConnectionPool
from redis.sentinel import Sentinel

logger = logging.getLogger(__name__)

class RedisPoolManager:
    """Redis连接池管理器"""
    
    def __init__(self, 
                 host: str = 'localhost',
                 port: int = 6379,
                 password: Optional[str] = None,
                 db: int = 0,
                 max_connections: int = 50,        # 增加最大连接数
                 max_connections_per_pool: int = 50,
                 retry_on_timeout: bool = True,
                 health_check_interval: int = 30,  # 健康检查间隔
                 socket_keepalive: bool = True,
                 socket_keepalive_options: Optional[Dict] = None):
        
        self.host = host
        self.port = port
        self.password = password
        self.db = db
        self.max_connections = max_connections
        self.max_connections_per_pool = max_connections_per_pool
        self.retry_on_timeout = retry_on_timeout
        self.health_check_interval = health_check_interval
        
        # 连接池配置
        self.pool_config = {
            'host': host,
            'port': port,
            'password': password,
            'db': db,
            'max_connections': max_connections_per_pool,
            'retry_on_timeout': retry_on_timeout,
            'socket_keepalive': socket_keepalive,
            'socket_keepalive_options': socket_keepalive_options or {},
            'socket_connect_timeout': 5,
            'socket_timeout': 5,
            'connection_pool_class': ConnectionPool,
            'decode_responses': False  # 保持字节格式以支持二进制数据
        }
        
        # 连接池实例
        self.pools: Dict[int, ConnectionPool] = {}
        self.clients: Dict[int, redis.Redis] = {}
        self.pool_lock = threading.RLock()
        
        # 连接统计
        self.stats = {
            'total_connections': 0,
            'active_connections': 0,
            'failed_connections': 0,
            'pool_hits': 0,
            'pool_misses': 0,
            'health_checks': 0,
            'health_check_failures': 0
        }
        
        # 启动健康检查
        self._start_health_check()
    
    def get_client(self, db: Optional[int] = None) -> redis.Redis:
        """获取Redis客户端"""
        target_db = db if db is not None else self.db
        
        with self.pool_lock:
            if target_db not in self.clients:
                self._create_pool_and_client(target_db)
            
            client = self.clients[target_db]
            self.stats['pool_hits'] += 1
            return client
    
    def _create_pool_and_client(self, db: int) -> None:
        """创建连接池和客户端"""
        try:
            # 创建连接池配置
            pool_config = self.pool_config.copy()
            pool_config['db'] = db
            
            # 创建连接池
            pool = ConnectionPool(**pool_config)
            self.pools[db] = pool
            
            # 创建Redis客户端
            client = redis.Redis(connection_pool=pool)
            self.clients[db] = client
            
            # 测试连接
            client.ping()
            
            self.stats['total_connections'] += 1
            logger.info(f"创建Redis连接池: DB={db}, 最大连接数={self.max_connections_per_pool}")
            
        except Exception as e:
            self.stats['failed_connections'] += 1
            logger.error(f"创建Redis连接池失败: DB={db}, 错误={e}")
            raise
    
    def get_pool_info(self, db: Optional[int] = None) -> Dict[str, Any]:
        """获取连接池信息"""
        target_db = db if db is not None else self.db
        
        with self.pool_lock:
            if target_db not in self.pools:
                return {'error': f'Pool for DB {target_db} not found'}
            
            pool = self.pools[target_db]
            
            return {
                'db': target_db,
                'max_connections': pool.max_connections,
                'created_connections': pool.created_connections,
                'available_connections': len(pool._available_connections),
                'in_use_connections': len(pool._in_use_connections),
                'pool_class': pool.__class__.__name__
            }
    
    def get_all_pools_info(self) -> Dict[int, Dict[str, Any]]:
        """获取所有连接池信息"""
        with self.pool_lock:
            return {db: self.get_pool_info(db) for db in self.pools.keys()}
    
    def health_check(self) -> Dict[str, Any]:
        """执行健康检查"""
        results = {}
        
        with self.pool_lock:
            for db, client in self.clients.items():
                try:
                    start_time = time.time()
                    client.ping()
                    response_time = time.time() - start_time
                    
                    results[db] = {
                        'status': 'healthy',
                        'response_time_ms': response_time * 1000,
                        'pool_info': self.get_pool_info(db)
                    }
                    
                except Exception as e:
                    results[db] = {
                        'status': 'unhealthy',
                        'error': str(e),
                        'pool_info': self.get_pool_info(db)
                    }
                    self.stats['health_check_failures'] += 1
        
        self.stats['health_checks'] += 1
        return results
    
    def _start_health_check(self) -> None:
        """启动健康检查线程"""
        def health_check_worker():
            while True:
                try:
                    time.sleep(self.health_check_interval)
                    health_results = self.health_check()
                    
                    # 检查是否有不健康的连接
                    unhealthy_dbs = [db for db, result in health_results.items() 
                                   if result['status'] == 'unhealthy']
                    
                    if unhealthy_dbs:
                        logger.warning(f"发现不健康的Redis连接: {unhealthy_dbs}")
                        # 尝试重新创建连接
                        self._recreate_unhealthy_connections(unhealthy_dbs)
                    
                except Exception as e:
                    logger.error(f"健康检查失败: {e}")
        
        health_thread = threading.Thread(target=health_check_worker, daemon=True)
        health_thread.start()
        logger.info(f"Redis健康检查已启动，间隔: {self.health_check_interval}秒")
    
    def _recreate_unhealthy_connections(self, unhealthy_dbs: list) -> None:
        """重新创建不健康的连接"""
        with self.pool_lock:
            for db in unhealthy_dbs:
                try:
                    # 关闭现有连接
                    if db in self.clients:
                        try:
                            self.clients[db].connection_pool.disconnect()
                        except:
                            pass
                        del self.clients[db]
                    
                    if db in self.pools:
                        try:
                            self.pools[db].disconnect()
                        except:
                            pass
                        del self.pools[db]
                    
                    # 重新创建
                    self._create_pool_and_client(db)
                    logger.info(f"重新创建Redis连接: DB={db}")
                    
                except Exception as e:
                    logger.error(f"重新创建Redis连接失败: DB={db}, 错误={e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        with self.pool_lock:
            stats = self.stats.copy()
            stats['active_pools'] = len(self.pools)
            stats['active_clients'] = len(self.clients)
            
            # 计算总的活跃连接数
            total_active = 0
            total_available = 0
            for pool in self.pools.values():
                total_active += len(pool._in_use_connections)
                total_available += len(pool._available_connections)
            
            stats['total_active_connections'] = total_active
            stats['total_available_connections'] = total_available
            stats['connection_utilization'] = total_active / (total_active + total_available) if (total_active + total_available) > 0 else 0
            
            return stats
    
    def close_all(self) -> None:
        """关闭所有连接"""
        with self.pool_lock:
            for client in self.clients.values():
                try:
                    client.connection_pool.disconnect()
                except:
                    pass
            
            for pool in self.pools.values():
                try:
                    pool.disconnect()
                except:
                    pass
            
            self.clients.clear()
            self.pools.clear()
            logger.info("所有Redis连接已关闭")

class RedisClusterManager(RedisPoolManager):
    """Redis集群管理器"""
    
    def __init__(self, 
                 startup_nodes: list,
                 password: Optional[str] = None,
                 max_connections: int = 50,
                 **kwargs):
        
        self.startup_nodes = startup_nodes
        super().__init__(password=password, max_connections=max_connections, **kwargs)
    
    def _create_pool_and_client(self, db: int) -> None:
        """为集群创建连接"""
        try:
            from rediscluster import RedisCluster
            
            client = RedisCluster(
                startup_nodes=self.startup_nodes,
                password=self.password,
                decode_responses=False,
                max_connections=self.max_connections,
                retry_on_timeout=self.retry_on_timeout
            )
            
            self.clients[db] = client
            client.ping()
            
            self.stats['total_connections'] += 1
            logger.info(f"创建Redis集群连接: 节点数={len(self.startup_nodes)}")
            
        except ImportError:
            logger.error("redis-py-cluster库未安装，无法使用集群模式")
            raise
        except Exception as e:
            self.stats['failed_connections'] += 1
            logger.error(f"创建Redis集群连接失败: {e}")
            raise

# 全局Redis连接池管理器
redis_pool_manager = None

def initialize_redis_pool(config: Dict[str, Any]) -> RedisPoolManager:
    """初始化Redis连接池"""
    global redis_pool_manager
    
    if 'cluster_nodes' in config:
        # 集群模式
        redis_pool_manager = RedisClusterManager(
            startup_nodes=config['cluster_nodes'],
            password=config.get('password'),
            max_connections=config.get('max_connections', 50)
        )
    else:
        # 单机模式
        redis_pool_manager = RedisPoolManager(
            host=config.get('host', 'localhost'),
            port=config.get('port', 6379),
            password=config.get('password'),
            db=config.get('db', 0),
            max_connections=config.get('max_connections', 50),
            max_connections_per_pool=config.get('max_connections_per_pool', 50)
        )
    
    logger.info("Redis连接池管理器已初始化")
    return redis_pool_manager

def get_redis_client(db: Optional[int] = None) -> redis.Redis:
    """获取Redis客户端"""
    if redis_pool_manager is None:
        raise RuntimeError("Redis连接池未初始化")
    return redis_pool_manager.get_client(db)
