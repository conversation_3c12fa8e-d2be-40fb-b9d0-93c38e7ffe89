#!/usr/bin/env python3
"""
Redis Streams管理器
实现基于Redis Streams的实时数据流处理
"""

import redis
import json
import time
import threading
import logging
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum

from .redis_pool_manager import get_redis_client
from .serialization_manager import serialization_manager

logger = logging.getLogger(__name__)

class StreamEventType(Enum):
    """流事件类型"""
    MODEL_UPDATE = "model_update"
    PREDICTION_REQUEST = "prediction_request"
    TRAINING_DATA = "training_data"
    SYSTEM_EVENT = "system_event"

@dataclass
class StreamMessage:
    """流消息数据类"""
    stream_name: str
    message_id: str
    event_type: StreamEventType
    device_id: str
    data: Dict[str, Any]
    timestamp: float

class RedisStreamsManager:
    """Redis Streams管理器"""
    
    def __init__(self, 
                 redis_db: int = 0,
                 stream_prefix: str = "bigdata",
                 consumer_group_prefix: str = "cg",
                 max_stream_length: int = 10000,
                 consumer_timeout: int = 5000):
        
        self.redis_db = redis_db
        self.stream_prefix = stream_prefix
        self.consumer_group_prefix = consumer_group_prefix
        self.max_stream_length = max_stream_length
        self.consumer_timeout = consumer_timeout
        
        # Redis客户端
        self.redis_client = get_redis_client(redis_db)
        
        # 流名称映射
        self.streams = {
            StreamEventType.MODEL_UPDATE: f"{stream_prefix}:model_updates",
            StreamEventType.PREDICTION_REQUEST: f"{stream_prefix}:predictions",
            StreamEventType.TRAINING_DATA: f"{stream_prefix}:training",
            StreamEventType.SYSTEM_EVENT: f"{stream_prefix}:system"
        }
        
        # 消费者组映射
        self.consumer_groups = {
            event_type: f"{consumer_group_prefix}:{stream_name.split(':')[-1]}"
            for event_type, stream_name in self.streams.items()
        }
        
        # 消费者线程管理
        self.consumers: Dict[str, threading.Thread] = {}
        self.consumer_callbacks: Dict[StreamEventType, List[Callable]] = {
            event_type: [] for event_type in StreamEventType
        }
        self.running = False
        
        # 统计信息
        self.stats = {
            'messages_produced': 0,
            'messages_consumed': 0,
            'processing_errors': 0,
            'consumer_lag': 0
        }
        
        # 初始化流和消费者组
        self._initialize_streams()
    
    def _initialize_streams(self):
        """初始化流和消费者组"""
        for event_type, stream_name in self.streams.items():
            try:
                # 创建消费者组（如果不存在）
                consumer_group = self.consumer_groups[event_type]
                try:
                    self.redis_client.xgroup_create(
                        stream_name, 
                        consumer_group, 
                        id='0', 
                        mkstream=True
                    )
                    logger.info(f"创建消费者组: {consumer_group} for {stream_name}")
                except redis.exceptions.ResponseError as e:
                    if "BUSYGROUP" in str(e):
                        logger.debug(f"消费者组已存在: {consumer_group}")
                    else:
                        raise
                
            except Exception as e:
                logger.error(f"初始化流失败: {stream_name}, 错误: {e}")
    
    def produce_message(self, 
                       event_type: StreamEventType,
                       device_id: str,
                       data: Dict[str, Any],
                       max_retries: int = 3) -> Optional[str]:
        """生产消息到流"""
        stream_name = self.streams[event_type]
        
        # 准备消息数据
        message_data = {
            'event_type': event_type.value,
            'device_id': device_id,
            'timestamp': time.time(),
            'data': serialization_manager.serialize(data).hex()  # 序列化并转换为hex字符串
        }
        
        for attempt in range(max_retries):
            try:
                # 发送消息到流
                message_id = self.redis_client.xadd(
                    stream_name,
                    message_data,
                    maxlen=self.max_stream_length,
                    approximate=True
                )
                
                self.stats['messages_produced'] += 1
                logger.debug(f"消息已发送: {stream_name}:{message_id}, 设备: {device_id}")
                return message_id.decode() if isinstance(message_id, bytes) else message_id
                
            except Exception as e:
                logger.error(f"发送消息失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(0.1 * (attempt + 1))  # 指数退避
        
        return None
    
    def register_consumer(self, 
                         event_type: StreamEventType,
                         callback: Callable[[StreamMessage], None],
                         consumer_name: Optional[str] = None) -> str:
        """注册消费者回调"""
        if consumer_name is None:
            consumer_name = f"consumer_{len(self.consumer_callbacks[event_type])}"
        
        self.consumer_callbacks[event_type].append(callback)
        logger.info(f"注册消费者: {consumer_name} for {event_type.value}")
        
        return consumer_name
    
    def start_consumers(self):
        """启动所有消费者"""
        if self.running:
            logger.warning("消费者已在运行")
            return
        
        self.running = True
        
        for event_type in StreamEventType:
            if self.consumer_callbacks[event_type]:
                consumer_thread = threading.Thread(
                    target=self._consumer_worker,
                    args=(event_type,),
                    daemon=True
                )
                consumer_thread.start()
                self.consumers[event_type.value] = consumer_thread
                logger.info(f"启动消费者线程: {event_type.value}")
    
    def stop_consumers(self):
        """停止所有消费者"""
        self.running = False
        
        # 等待所有消费者线程结束
        for thread in self.consumers.values():
            thread.join(timeout=5)
        
        self.consumers.clear()
        logger.info("所有消费者已停止")
    
    def _consumer_worker(self, event_type: StreamEventType):
        """消费者工作线程"""
        stream_name = self.streams[event_type]
        consumer_group = self.consumer_groups[event_type]
        consumer_name = f"consumer_{threading.current_thread().ident}"
        
        logger.info(f"消费者工作线程启动: {consumer_name} for {event_type.value}")
        
        while self.running:
            try:
                # 从流中读取消息
                messages = self.redis_client.xreadgroup(
                    consumer_group,
                    consumer_name,
                    {stream_name: '>'},
                    count=10,
                    block=self.consumer_timeout
                )
                
                if not messages:
                    continue
                
                # 处理消息
                for stream, stream_messages in messages:
                    for message_id, fields in stream_messages:
                        try:
                            self._process_message(event_type, stream_name, message_id, fields)
                            
                            # 确认消息处理完成
                            self.redis_client.xack(stream_name, consumer_group, message_id)
                            self.stats['messages_consumed'] += 1
                            
                        except Exception as e:
                            logger.error(f"处理消息失败: {message_id}, 错误: {e}")
                            self.stats['processing_errors'] += 1
                
            except redis.exceptions.ConnectionError:
                logger.warning("Redis连接断开，尝试重连...")
                time.sleep(1)
            except Exception as e:
                logger.error(f"消费者工作线程错误: {e}")
                time.sleep(1)
        
        logger.info(f"消费者工作线程结束: {consumer_name}")
    
    def _process_message(self, 
                        event_type: StreamEventType,
                        stream_name: str,
                        message_id: bytes,
                        fields: Dict[bytes, bytes]):
        """处理单个消息"""
        try:
            # 解析消息字段
            message_data = {k.decode(): v.decode() for k, v in fields.items()}
            
            # 反序列化数据
            data_hex = message_data.get('data', '')
            if data_hex:
                data_bytes = bytes.fromhex(data_hex)
                data = serialization_manager.deserialize(data_bytes)
            else:
                data = {}
            
            # 创建流消息对象
            stream_message = StreamMessage(
                stream_name=stream_name,
                message_id=message_id.decode(),
                event_type=event_type,
                device_id=message_data.get('device_id', ''),
                data=data,
                timestamp=float(message_data.get('timestamp', 0))
            )
            
            # 调用所有注册的回调函数
            for callback in self.consumer_callbacks[event_type]:
                try:
                    callback(stream_message)
                except Exception as e:
                    logger.error(f"回调函数执行失败: {callback.__name__}, 错误: {e}")
        
        except Exception as e:
            logger.error(f"解析消息失败: {message_id}, 错误: {e}")
            raise
    
    def get_stream_info(self, event_type: StreamEventType) -> Dict[str, Any]:
        """获取流信息"""
        stream_name = self.streams[event_type]
        
        try:
            info = self.redis_client.xinfo_stream(stream_name)
            return {
                'stream_name': stream_name,
                'length': info[b'length'],
                'first_entry': info[b'first-entry'],
                'last_entry': info[b'last-entry'],
                'consumer_groups': info[b'groups']
            }
        except Exception as e:
            logger.error(f"获取流信息失败: {stream_name}, 错误: {e}")
            return {}
    
    def get_consumer_group_info(self, event_type: StreamEventType) -> Dict[str, Any]:
        """获取消费者组信息"""
        stream_name = self.streams[event_type]
        consumer_group = self.consumer_groups[event_type]
        
        try:
            groups = self.redis_client.xinfo_groups(stream_name)
            for group in groups:
                if group[b'name'].decode() == consumer_group:
                    return {
                        'name': group[b'name'].decode(),
                        'consumers': group[b'consumers'],
                        'pending': group[b'pending'],
                        'last_delivered_id': group[b'last-delivered-id'].decode()
                    }
        except Exception as e:
            logger.error(f"获取消费者组信息失败: {consumer_group}, 错误: {e}")
        
        return {}
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        
        # 计算消费者延迟
        total_lag = 0
        for event_type in StreamEventType:
            try:
                stream_info = self.get_stream_info(event_type)
                consumer_info = self.get_consumer_group_info(event_type)
                
                if stream_info and consumer_info:
                    # 简化的延迟计算
                    lag = consumer_info.get('pending', 0)
                    total_lag += lag
            except:
                pass
        
        stats['consumer_lag'] = total_lag
        stats['active_consumers'] = len([t for t in self.consumers.values() if t.is_alive()])
        
        return stats
    
    def cleanup_old_messages(self, max_age_hours: int = 24):
        """清理旧消息"""
        cutoff_time = int((time.time() - max_age_hours * 3600) * 1000)
        
        for event_type, stream_name in self.streams.items():
            try:
                # 删除指定时间之前的消息
                self.redis_client.xtrim(stream_name, minid=cutoff_time, approximate=True)
                logger.info(f"清理旧消息: {stream_name}, 截止时间: {max_age_hours}小时前")
            except Exception as e:
                logger.error(f"清理旧消息失败: {stream_name}, 错误: {e}")

# 全局Redis Streams管理器实例
redis_streams_manager = None

def initialize_streams_manager(redis_db: int = 0) -> RedisStreamsManager:
    """初始化Redis Streams管理器"""
    global redis_streams_manager
    redis_streams_manager = RedisStreamsManager(redis_db=redis_db)
    logger.info("Redis Streams管理器已初始化")
    return redis_streams_manager

def get_streams_manager() -> Optional[RedisStreamsManager]:
    """获取Redis Streams管理器实例"""
    return redis_streams_manager
