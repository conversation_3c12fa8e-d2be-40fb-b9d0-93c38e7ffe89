#!/usr/bin/env python3
"""
序列化集成测试
测试序列化优化与现有系统的集成情况
"""

import sys
import os
import time
import unittest
import tempfile
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from DBUtil.serialization_manager import SerializationManager, SerializationMethod
from DBUtil.Redis import RedisModelManager

class SerializationIntegrationTest(unittest.TestCase):
    """序列化集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.serializer = SerializationManager()
        
        # 创建测试用的模型数据
        self.test_models = {
            'shouwei_model': {
                'model_type': 'shouwei_lasu_control',
                'device_id': 'shouwei_001',
                'power_baseline': [100.0, 105.0, 110.0, 115.0],
                'lasu_baseline': [2.5, 2.6, 2.7, 2.8],
                'weight_corrections': {
                    'weight_11': [0.1, 0.2, 0.3],
                    'weight_12': [0.15, 0.25, 0.35]
                },
                'last_loaded': time.time()
            },
            'kongwen_model': {
                'model_type': 'kongwen_power_control',
                'device_id': 'kongwen_001',
                'main_power_model': {
                    'coefficients': [1.2, 0.8, 0.5, 0.3],
                    'intercept': 50.0
                },
                'vice_power_model': {
                    'coefficients': [0.8, 0.6, 0.4, 0.2],
                    'intercept': 30.0
                },
                'feature_names': ['t', 'ratio', 'ccd', 'ccd3', 'fullmelting'],
                'last_loaded': time.time()
            }
        }
    
    def test_backward_compatibility(self):
        """测试向后兼容性"""
        print("\n测试向后兼容性...")
        
        for model_name, model_data in self.test_models.items():
            with self.subTest(model=model_name):
                # 使用Pickle序列化
                pickle_data = self.serializer.serialize(model_data, SerializationMethod.PICKLE)
                
                # 使用自动检测反序列化
                restored_data = self.serializer.deserialize(pickle_data)
                
                # 验证数据完整性
                self.assertEqual(restored_data['model_type'], model_data['model_type'])
                self.assertEqual(restored_data['device_id'], model_data['device_id'])
                
                print(f"  ✓ {model_name} 向后兼容性测试通过")
    
    def test_format_auto_detection(self):
        """测试格式自动检测"""
        print("\n测试格式自动检测...")
        
        model_data = self.test_models['shouwei_model']
        
        # 测试不同格式的自动检测
        formats = [
            (SerializationMethod.PICKLE, "Pickle"),
            (SerializationMethod.MSGPACK, "MessagePack"),
            (SerializationMethod.MSGPACK_LZ4, "MessagePack+LZ4")
        ]
        
        for method, method_name in formats:
            with self.subTest(format=method_name):
                try:
                    # 序列化
                    serialized_data = self.serializer.serialize(model_data, method)
                    
                    # 自动检测并反序列化
                    restored_data = self.serializer.deserialize(serialized_data)
                    
                    # 验证数据完整性
                    self.assertEqual(restored_data['model_type'], model_data['model_type'])
                    print(f"  ✓ {method_name} 格式自动检测通过")
                    
                except ImportError:
                    print(f"  - {method_name} 库未安装，跳过测试")
    
    def test_redis_manager_integration(self):
        """测试与Redis管理器的集成"""
        print("\n测试Redis管理器集成...")
        
        # 模拟Redis连接
        mock_redis = Mock()
        mock_redis.set = Mock(return_value=True)
        mock_redis.get = Mock()
        
        with patch('redis.StrictRedis', return_value=mock_redis):
            # 创建Redis管理器
            redis_manager = RedisModelManager(
                redis_host='localhost',
                redis_port=6379,
                redis_password='',
                redis_db=0,
                use_connection_pool=False
            )
            
            # 测试模型保存
            model_data = self.test_models['shouwei_model']
            redis_manager.save_model('test_model', 'device_001', model_data, 'v1')
            
            # 验证Redis被调用
            self.assertTrue(mock_redis.set.called)
            
            # 获取保存的数据
            call_args = mock_redis.set.call_args
            saved_key = call_args[0][0]
            saved_data = call_args[0][1]
            
            self.assertEqual(saved_key, 'test_model:device_001:v1')
            self.assertIsInstance(saved_data, bytes)
            
            # 模拟Redis返回数据
            mock_redis.get.return_value = saved_data
            
            # 测试模型加载
            loaded_model = redis_manager.load_model('test_model', 'device_001', 'v1')
            
            # 验证数据完整性
            self.assertIsNotNone(loaded_model)
            self.assertEqual(loaded_model['model_type'], model_data['model_type'])
            
            print("  ✓ Redis管理器集成测试通过")
    
    def test_performance_statistics(self):
        """测试性能统计功能"""
        print("\n测试性能统计功能...")
        
        # 重置统计
        self.serializer.reset_stats()
        
        # 执行多次序列化操作
        model_data = self.test_models['kongwen_model']
        
        for i in range(10):
            serialized = self.serializer.serialize(model_data)
            self.serializer.deserialize(serialized)
        
        # 获取统计信息
        stats = self.serializer.get_stats()
        
        # 验证统计数据
        self.assertEqual(stats['serialize_count'], 10)
        self.assertEqual(stats['deserialize_count'], 10)
        self.assertGreater(stats['avg_serialize_time_ms'], 0)
        self.assertGreater(stats['avg_deserialize_time_ms'], 0)
        
        print(f"  ✓ 统计功能测试通过")
        print(f"    序列化次数: {stats['serialize_count']}")
        print(f"    平均序列化时间: {stats['avg_serialize_time_ms']:.2f} ms")
        print(f"    平均反序列化时间: {stats['avg_deserialize_time_ms']:.2f} ms")
    
    def test_fallback_mechanism(self):
        """测试降级机制"""
        print("\n测试降级机制...")
        
        # 模拟优化库不可用的情况
        with patch('DBUtil.serialization_manager.OPTIMIZATION_AVAILABLE', False):
            fallback_serializer = SerializationManager()
            
            model_data = self.test_models['shouwei_model']
            
            # 尝试使用优化方法，应该自动降级到Pickle
            serialized = fallback_serializer.serialize(model_data, SerializationMethod.MSGPACK_LZ4)
            deserialized = fallback_serializer.deserialize(serialized)
            
            # 验证数据完整性
            self.assertEqual(deserialized['model_type'], model_data['model_type'])
            
            # 检查是否使用了降级
            stats = fallback_serializer.get_stats()
            self.assertGreater(stats.get('fallback_count', 0), 0)
            
            print("  ✓ 降级机制测试通过")
    
    def test_large_model_handling(self):
        """测试大模型处理"""
        print("\n测试大模型处理...")
        
        # 创建大模型数据（约5MB）
        import numpy as np
        large_model = {
            'model_type': 'large_neural_network',
            'device_id': 'large_device_001',
            'weights': {
                'layer1': np.random.randn(1000, 1000).tolist(),
                'layer2': np.random.randn(1000, 500).tolist(),
                'layer3': np.random.randn(500, 100).tolist()
            },
            'metadata': {
                'training_samples': 1000000,
                'features': [f'feature_{i}' for i in range(10000)]
            }
        }
        
        # 测试序列化和反序列化
        start_time = time.time()
        serialized = self.serializer.serialize(large_model)
        serialize_time = time.time() - start_time
        
        start_time = time.time()
        deserialized = self.serializer.deserialize(serialized)
        deserialize_time = time.time() - start_time
        
        # 验证数据完整性
        self.assertEqual(deserialized['model_type'], large_model['model_type'])
        self.assertEqual(len(deserialized['weights']['layer1']), len(large_model['weights']['layer1']))
        
        print(f"  ✓ 大模型处理测试通过")
        print(f"    序列化时间: {serialize_time:.2f} 秒")
        print(f"    反序列化时间: {deserialize_time:.2f} 秒")
        print(f"    数据大小: {len(serialized) / (1024*1024):.2f} MB")
    
    def test_concurrent_access(self):
        """测试并发访问"""
        print("\n测试并发访问...")
        
        import threading
        import queue
        
        model_data = self.test_models['shouwei_model']
        results_queue = queue.Queue()
        error_queue = queue.Queue()
        
        def worker():
            try:
                for _ in range(10):
                    serialized = self.serializer.serialize(model_data)
                    deserialized = self.serializer.deserialize(serialized)
                    results_queue.put(deserialized['model_type'])
            except Exception as e:
                error_queue.put(str(e))
        
        # 启动多个线程
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=worker)
            thread.start()
            threads.append(thread)
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 检查结果
        self.assertTrue(error_queue.empty(), f"并发测试出现错误: {list(error_queue.queue)}")
        
        # 验证所有结果
        results = []
        while not results_queue.empty():
            results.append(results_queue.get())
        
        self.assertEqual(len(results), 50)  # 5个线程 × 10次操作
        self.assertTrue(all(r == model_data['model_type'] for r in results))
        
        print(f"  ✓ 并发访问测试通过 ({len(results)} 次操作)")
    
    def test_error_handling(self):
        """测试错误处理"""
        print("\n测试错误处理...")
        
        # 测试无效数据
        with self.assertRaises(Exception):
            self.serializer.deserialize(b'invalid_data')
        
        # 测试空数据
        with self.assertRaises(Exception):
            self.serializer.deserialize(b'')
        
        # 验证错误统计
        stats = self.serializer.get_stats()
        self.assertGreater(stats['error_count'], 0)
        
        print("  ✓ 错误处理测试通过")

class RealWorldScenarioTest(unittest.TestCase):
    """真实场景测试"""
    
    def test_shouwei_control_scenario(self):
        """测试首尾控制场景"""
        print("\n测试首尾控制真实场景...")
        
        serializer = SerializationManager()
        
        # 模拟首尾控制的真实数据
        shouwei_data = {
            'model_type': 'shouwei_lasu_control',
            'device_id': 'shouwei_production_001',
            'power_baseline': [98.5, 102.3, 107.8, 112.1, 116.9],
            'lasu_baseline': [2.45, 2.52, 2.68, 2.74, 2.81],
            'weight_corrections': {
                'weight_11': [0.12, 0.18, 0.25, 0.31, 0.28],
                'weight_12': [0.16, 0.22, 0.29, 0.35, 0.32],
                'weight_105': [0.08, 0.14, 0.21, 0.27, 0.24]
            },
            'calibration_data': {
                'height_range': [120, 180],
                'weight_range': [80, 150],
                'optimal_power': 105.0,
                'optimal_lasu': 2.6
            },
            'last_loaded': time.time(),
            'version': 'v2.1',
            'accuracy': 0.94
        }
        
        # 测试序列化性能
        start_time = time.time()
        serialized = serializer.serialize(shouwei_data)
        serialize_time = (time.time() - start_time) * 1000
        
        # 测试反序列化性能
        start_time = time.time()
        deserialized = serializer.deserialize(serialized)
        deserialize_time = (time.time() - start_time) * 1000
        
        # 验证数据完整性
        self.assertEqual(deserialized['device_id'], shouwei_data['device_id'])
        self.assertEqual(len(deserialized['power_baseline']), len(shouwei_data['power_baseline']))
        self.assertEqual(deserialized['accuracy'], shouwei_data['accuracy'])
        
        print(f"  ✓ 首尾控制场景测试通过")
        print(f"    序列化时间: {serialize_time:.2f} ms")
        print(f"    反序列化时间: {deserialize_time:.2f} ms")
        print(f"    数据大小: {len(serialized)} bytes")
    
    def test_kongwen_control_scenario(self):
        """测试控温场景"""
        print("\n测试控温真实场景...")
        
        serializer = SerializationManager()
        
        # 模拟控温的真实数据
        kongwen_data = {
            'model_type': 'kongwen_power_control',
            'device_id': 'kongwen_production_001',
            'main_power_model': {
                'coefficients': [1.25, 0.83, 0.52, 0.31, 0.18],
                'intercept': 48.7,
                'r_squared': 0.92
            },
            'vice_power_model': {
                'coefficients': [0.78, 0.61, 0.44, 0.23, 0.12],
                'intercept': 28.3,
                'r_squared': 0.89
            },
            'feature_names': ['t', 'ratio', 'ccd', 'ccd3', 'fullmelting', 'sum_jialiao_time'],
            'temperature_curves': {
                'heating_curve': [1200, 1250, 1300, 1350, 1400],
                'cooling_curve': [1400, 1350, 1300, 1250, 1200]
            },
            'control_parameters': {
                'max_power': 150.0,
                'min_power': 30.0,
                'power_step': 5.0,
                'response_time': 2.5
            },
            'last_loaded': time.time(),
            'version': 'v1.8',
            'validation_accuracy': 0.91
        }
        
        # 测试序列化性能
        start_time = time.time()
        serialized = serializer.serialize(kongwen_data)
        serialize_time = (time.time() - start_time) * 1000
        
        # 测试反序列化性能
        start_time = time.time()
        deserialized = serializer.deserialize(serialized)
        deserialize_time = (time.time() - start_time) * 1000
        
        # 验证数据完整性
        self.assertEqual(deserialized['device_id'], kongwen_data['device_id'])
        self.assertEqual(len(deserialized['feature_names']), len(kongwen_data['feature_names']))
        self.assertEqual(deserialized['validation_accuracy'], kongwen_data['validation_accuracy'])
        
        print(f"  ✓ 控温场景测试通过")
        print(f"    序列化时间: {serialize_time:.2f} ms")
        print(f"    反序列化时间: {deserialize_time:.2f} ms")
        print(f"    数据大小: {len(serialized)} bytes")

if __name__ == '__main__':
    # 运行集成测试
    unittest.main(verbosity=2)
