import pickle
from datetime import datetime
import os
import tempfile
import time
import logging
from .smart_cache_manager import smart_cache_manager

DB_PATH = "/dev/shm/mydb.sqlite"  # 在 RAM 磁盘中创建 SQLite 数据库
#DB_PATH = os.path.join(tempfile.gettempdir(), "mydb.sqlite")
import sqlite3
from flask import g

logger = logging.getLogger(__name__)

def get_db():
    """获取 SQLite 数据库连接"""
    if "db" not in g:
        g.db = sqlite3.connect(DB_PATH, check_same_thread=False)
        g.db.execute("PRAGMA journal_mode=WAL;")  # 启用 WAL
        g.db.execute("""
            CREATE TABLE IF NOT EXISTS model_map (
                gongxu TEXT,
                device_id TEXT,
                model_data BLOB,
                last_access_time REAL,
                access_count INTEGER DEFAULT 0,
                ttl INTEGER DEFAULT 180,
                created_time REAL,
                PRIMARY KEY (gongxu, device_id)
            )
        """)

        # 添加新列（如果不存在）
        try:
            g.db.execute("ALTER TABLE model_map ADD COLUMN last_access_time REAL")
        except sqlite3.OperationalError:
            pass  # 列已存在

        try:
            g.db.execute("ALTER TABLE model_map ADD COLUMN access_count INTEGER DEFAULT 0")
        except sqlite3.OperationalError:
            pass

        try:
            g.db.execute("ALTER TABLE model_map ADD COLUMN ttl INTEGER DEFAULT 180")
        except sqlite3.OperationalError:
            pass

        try:
            g.db.execute("ALTER TABLE model_map ADD COLUMN created_time REAL")
        except sqlite3.OperationalError:
            pass
        g.db.commit()
    return g.db

def close_db():
    db = g.pop("db", None)
    if db is not None:
        db.close()

def save_model_to_db(conn, gongxu, device_id, model_data):
    """保存模型到 SQLite（支持智能缓存）"""
    model_data_blob = pickle.dumps(model_data)
    cursor = conn.cursor()
    current_time = time.time()

    # 计算智能TTL
    smart_ttl = smart_cache_manager.calculate_smart_ttl(device_id)

    # 检查是否已存在
    cursor.execute("SELECT access_count FROM model_map WHERE gongxu=? AND device_id=?",
                   (gongxu, device_id))
    row = cursor.fetchone()
    access_count = (row[0] + 1) if row else 1

    cursor.execute("""
        INSERT OR REPLACE INTO model_map
        (gongxu, device_id, model_data, last_access_time, access_count, ttl, created_time)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    """, (gongxu, device_id, model_data_blob, current_time, access_count, smart_ttl, current_time))

    conn.commit()
    logger.debug(f"模型已保存: {gongxu}:{device_id}, TTL: {smart_ttl}s")

def load_model_from_db(conn, gongxu, device_id):
    """从 SQLite 加载模型（支持智能缓存和TTL检查）"""
    cursor = conn.cursor()
    current_time = time.time()

    cursor.execute("""
        SELECT gongxu, device_id, model_data, last_access_time, access_count, ttl, created_time
        FROM model_map WHERE gongxu=? AND device_id=?
    """, (gongxu, device_id))

    row = cursor.fetchone()
    if row is None:
        smart_cache_manager.record_access(device_id, cache_hit=False)
        return None, None, None

    gongxu_val, device_id_val, model_data_blob, last_access, access_count, ttl, created_time = row

    # 检查TTL是否过期
    if last_access and (current_time - last_access) > ttl:
        logger.debug(f"缓存过期: {gongxu}:{device_id}, 过期时间: {current_time - last_access:.1f}s")
        # 删除过期缓存
        cursor.execute("DELETE FROM model_map WHERE gongxu=? AND device_id=?", (gongxu, device_id))
        conn.commit()
        smart_cache_manager.record_access(device_id, cache_hit=False)
        return None, None, None

    # 更新访问信息
    new_access_count = access_count + 1
    cursor.execute("""
        UPDATE model_map
        SET last_access_time=?, access_count=?
        WHERE gongxu=? AND device_id=?
    """, (current_time, new_access_count, gongxu, device_id))
    conn.commit()

    # 记录访问模式
    smart_cache_manager.record_access(device_id, cache_hit=True)

    try:
        model_data = pickle.loads(model_data_blob)
        logger.debug(f"缓存命中: {gongxu}:{device_id}, 访问次数: {new_access_count}")
        return gongxu_val, device_id_val, model_data
    except Exception as e:
        logger.error(f"反序列化失败: {e}")
        return None, None, None
def delete_from_db(conn, gongxu, device_id):
    """从数据库删除模型"""
    cursor = conn.cursor()
    cursor.execute("DELETE FROM model_map WHERE gongxu=? AND device_id=?", (gongxu, device_id))
    conn.commit()

def cleanup_expired_cache(conn):
    """清理过期缓存"""
    cursor = conn.cursor()
    current_time = time.time()

    # 查找过期的缓存项
    cursor.execute("""
        SELECT gongxu, device_id, last_access_time, ttl
        FROM model_map
        WHERE last_access_time IS NOT NULL
    """)

    expired_items = []
    for row in cursor.fetchall():
        gongxu, device_id, last_access, ttl = row
        if (current_time - last_access) > ttl:
            expired_items.append((gongxu, device_id))

    # 删除过期项
    for gongxu, device_id in expired_items:
        cursor.execute("DELETE FROM model_map WHERE gongxu=? AND device_id=?", (gongxu, device_id))
        logger.debug(f"清理过期缓存: {gongxu}:{device_id}")

    if expired_items:
        conn.commit()
        logger.info(f"清理了 {len(expired_items)} 个过期缓存项")

def get_cache_statistics(conn):
    """获取缓存统计信息"""
    cursor = conn.cursor()
    current_time = time.time()

    # 总缓存项数
    cursor.execute("SELECT COUNT(*) FROM model_map")
    total_items = cursor.fetchone()[0]

    # 活跃缓存项数（最近1小时访问过的）
    cursor.execute("""
        SELECT COUNT(*) FROM model_map
        WHERE last_access_time IS NOT NULL AND (? - last_access_time) < 3600
    """, (current_time,))
    active_items = cursor.fetchone()[0]

    # 高频访问项数（访问次数 > 10）
    cursor.execute("SELECT COUNT(*) FROM model_map WHERE access_count > 10")
    high_frequency_items = cursor.fetchone()[0]

    # 平均TTL
    cursor.execute("SELECT AVG(ttl) FROM model_map WHERE ttl IS NOT NULL")
    avg_ttl = cursor.fetchone()[0] or 0

    return {
        'total_items': total_items,
        'active_items': active_items,
        'high_frequency_items': high_frequency_items,
        'avg_ttl': avg_ttl,
        'cache_manager_stats': smart_cache_manager.get_cache_stats()
    }

def preload_popular_models(conn, redis_manager, max_preloads=5):
    """预加载热门模型"""
    cursor = conn.cursor()

    # 查找高频访问的模型
    cursor.execute("""
        SELECT gongxu, device_id, access_count, last_access_time
        FROM model_map
        WHERE access_count >= 5
        ORDER BY access_count DESC, last_access_time DESC
        LIMIT ?
    """, (max_preloads,))

    preloaded_count = 0
    for row in cursor.fetchall():
        gongxu, device_id, access_count, last_access = row

        # 检查是否应该预加载
        if smart_cache_manager.should_preload(device_id):
            try:
                # 这里需要与具体的模型加载逻辑集成
                logger.info(f"预加载模型: {gongxu}:{device_id} (访问次数: {access_count})")
                preloaded_count += 1
            except Exception as e:
                logger.error(f"预加载失败: {gongxu}:{device_id}, 错误: {e}")

    if preloaded_count > 0:
        smart_cache_manager.cache_stats['preloads'] += preloaded_count
        logger.info(f"完成预加载: {preloaded_count} 个模型")