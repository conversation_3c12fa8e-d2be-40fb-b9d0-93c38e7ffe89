{"id": "industrial-control-env", "name": "Industrial Control Environment", "values": [{"key": "base_url", "value": "http://localhost:5000", "description": "API服务器基础URL", "type": "default", "enabled": true}, {"key": "api_version", "value": "v1", "description": "API版本", "type": "default", "enabled": true}, {"key": "json_content_type", "value": "application/json", "description": "JSON请求Content-Type", "type": "default", "enabled": true}, {"key": "protobuf_content_type", "value": "application/x-protobuf", "description": "Protobuf请求Content-Type", "type": "default", "enabled": true}, {"key": "timestamp", "value": "{{$timestamp}}", "description": "动态时间戳", "type": "default", "enabled": true}, {"key": "request_id", "value": "req_{{$randomAlphaNumeric}}", "description": "动态请求ID", "type": "default", "enabled": true}, {"key": "test_device_shouwei", "value": "shouwei_device_001", "description": "首尾控制测试设备ID", "type": "default", "enabled": true}, {"key": "test_device_kongwen", "value": "kongwen_device_001", "description": "控温测试设备ID", "type": "default", "enabled": true}, {"key": "performance_iterations", "value": "100", "description": "性能测试迭代次数", "type": "default", "enabled": true}], "_postman_variable_scope": "environment"}