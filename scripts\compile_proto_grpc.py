#!/usr/bin/env python3
"""
使用grpcio-tools编译proto文件
"""

import os
import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def compile_with_grpc_tools():
    """使用grpcio-tools编译proto文件"""
    try:
        from grpc_tools import protoc
        logger.info("使用grpcio-tools编译proto文件...")
        
        # 项目根目录
        project_root = Path(__file__).parent.parent
        proto_dir = project_root / "proto"
        output_dir = project_root / "generated" / "python"
        
        # 确保输出目录存在
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 查找所有proto文件
        proto_files = list(proto_dir.rglob("*.proto"))
        
        if not proto_files:
            logger.warning("未找到proto文件")
            return False
        
        logger.info(f"找到 {len(proto_files)} 个proto文件")
        
        # 编译每个proto文件
        for proto_file in proto_files:
            logger.info(f"编译: {proto_file}")
            
            # 相对于proto目录的路径
            relative_path = proto_file.relative_to(proto_dir)
            
            # 使用grpc_tools.protoc编译
            result = protoc.main([
                'protoc',
                f'--proto_path={proto_dir}',
                f'--python_out={output_dir}',
                f'--grpc_python_out={output_dir}',
                str(relative_path)
            ])
            
            if result == 0:
                logger.info(f"编译成功: {proto_file.name}")
            else:
                logger.error(f"编译失败: {proto_file.name}")
                return False
        
        return True
        
    except ImportError:
        logger.error("grpcio-tools未安装，请运行: pip install grpcio-tools")
        return False
    except Exception as e:
        logger.error(f"编译异常: {e}")
        return False

def create_init_files():
    """创建__init__.py文件"""
    logger.info("创建__init__.py文件...")
    
    project_root = Path(__file__).parent.parent
    generated_dir = project_root / "generated" / "python"
    
    # 需要创建__init__.py的目录
    init_dirs = [
        generated_dir,
        generated_dir / "industrial_control",
        generated_dir / "industrial_control" / "v1"
    ]
    
    for init_dir in init_dirs:
        if init_dir.exists():
            init_file = init_dir / "__init__.py"
            if not init_file.exists():
                init_file.write_text("# Generated protobuf package\n")
                logger.info(f"创建: {init_file}")

def copy_generated_files():
    """复制生成的文件到DBUtil目录"""
    logger.info("复制生成的文件...")
    
    project_root = Path(__file__).parent.parent
    source_dir = project_root / "generated" / "python" / "industrial_control"
    target_dir = project_root / "DBUtil" / "industrial_control"
    
    if source_dir.exists():
        import shutil
        if target_dir.exists():
            shutil.rmtree(target_dir)
        shutil.copytree(source_dir, target_dir)
        logger.info(f"复制完成: {source_dir} -> {target_dir}")
        return True
    else:
        logger.warning("生成的文件目录不存在")
        return False

def main():
    """主函数"""
    logger.info("开始使用grpcio-tools编译proto文件...")
    
    # 1. 编译proto文件
    if not compile_with_grpc_tools():
        logger.error("proto文件编译失败")
        return False
    
    # 2. 创建__init__.py文件
    create_init_files()
    
    # 3. 复制生成的文件
    if not copy_generated_files():
        logger.error("文件复制失败")
        return False
    
    logger.info("🎉 Proto文件编译完成！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
