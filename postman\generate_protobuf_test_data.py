#!/usr/bin/env python3
"""
生成Protobuf测试数据
为Postman测试生成二进制Protobuf文件
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def generate_shouwei_protobuf_data():
    """生成首尾控制Protobuf测试数据"""
    from DBUtil.industrial_control.v1 import shouwei_service_pb2
    
    # 创建首尾控制请求
    request = shouwei_service_pb2.ShouweiPredictionRequest()
    
    # 设备信息
    request.device_info.device_id = "shouwei_device_001"
    request.device_info.device_type = "shouwei_lasu_control"
    request.device_info.location = "production_line_1"
    
    # 传感器数据
    request.sensor_data.temperature = 25.5
    request.sensor_data.pressure = 1.2
    request.sensor_data.flow_rate = 15.8
    request.sensor_data.power_consumption = 850.0
    request.sensor_data.humidity = 65.2
    request.sensor_data.vibration = 0.05
    
    # 控制参数
    request.control_parameters.target_temperature = 26.0
    request.control_parameters.max_power_limit = 1000
    request.control_parameters.control_precision = 0.1
    request.control_parameters.emergency_stop = False
    
    # 首尾控制特有数据
    request.shouwei_data.lasu_coefficient = 1.25
    request.shouwei_data.baseline_power = 500.0
    request.shouwei_data.weight_corrections.extend([0.95, 1.02, 0.98, 1.01, 0.99])
    
    # 功率分配
    request.shouwei_data.power_distribution.main_ratio = 0.7
    request.shouwei_data.power_distribution.vice_ratio = 0.25
    request.shouwei_data.power_distribution.reserve_ratio = 0.05
    
    return request.SerializeToString()

def generate_kongwen_protobuf_data():
    """生成控温Protobuf测试数据"""
    from DBUtil.industrial_control.v1 import kongwen_service_pb2
    
    # 创建控温请求
    request = kongwen_service_pb2.KongwenPredictionRequest()
    
    # 设备信息
    request.device_info.device_id = "kongwen_device_001"
    request.device_info.device_type = "kongwen_power_control"
    request.device_info.location = "thermal_control_zone_A"
    
    # 传感器数据
    request.sensor_data.temperature = 24.8
    request.sensor_data.pressure = 1.1
    request.sensor_data.flow_rate = 12.5
    request.sensor_data.power_consumption = 750.0
    request.sensor_data.humidity = 58.3
    request.sensor_data.vibration = 0.03
    
    # 控制参数
    request.control_parameters.target_temperature = 25.5
    request.control_parameters.max_power_limit = 800
    request.control_parameters.control_precision = 0.05
    request.control_parameters.emergency_stop = False
    
    # 控温特有数据
    request.kongwen_data.thermal_capacity = 1000.0
    request.kongwen_data.heat_transfer_coefficient = 10.0
    request.kongwen_data.ambient_temperature = 20.0
    
    # 热力学模型
    request.kongwen_data.thermal_model.time_constant = 120.0
    request.kongwen_data.thermal_model.gain = 0.85
    request.kongwen_data.thermal_model.dead_time = 5.0
    request.kongwen_data.thermal_model.pid_parameters.extend([1.5, 0.6, 0.2])
    
    return request.SerializeToString()

def save_binary_files():
    """保存二进制文件供Postman使用"""
    postman_dir = Path(__file__).parent
    
    # 生成首尾控制数据
    shouwei_data = generate_shouwei_protobuf_data()
    shouwei_file = postman_dir / "shouwei_request.bin"
    with open(shouwei_file, 'wb') as f:
        f.write(shouwei_data)
    print(f"✓ 生成首尾控制Protobuf数据: {shouwei_file} ({len(shouwei_data)} bytes)")
    
    # 生成控温数据
    kongwen_data = generate_kongwen_protobuf_data()
    kongwen_file = postman_dir / "kongwen_request.bin"
    with open(kongwen_file, 'wb') as f:
        f.write(kongwen_data)
    print(f"✓ 生成控温Protobuf数据: {kongwen_file} ({len(kongwen_data)} bytes)")
    
    return shouwei_file, kongwen_file

def generate_curl_examples():
    """生成curl命令示例"""
    postman_dir = Path(__file__).parent
    
    curl_examples = f"""
# Protobuf测试curl命令示例

## 1. 首尾控制 - JSON请求
curl -X POST http://localhost:5000/api/v1/shouwei/predict \\
  -H "Content-Type: application/json" \\
  -H "Accept: application/json" \\
  -d @{postman_dir}/shouwei_request.json

## 2. 首尾控制 - Protobuf请求
curl -X POST http://localhost:5000/api/v1/shouwei/predict \\
  -H "Content-Type: application/x-protobuf" \\
  -H "Accept: application/x-protobuf" \\
  --data-binary @{postman_dir}/shouwei_request.bin

## 3. 控温 - JSON请求
curl -X POST http://localhost:5000/api/v1/kongwen/predict \\
  -H "Content-Type: application/json" \\
  -H "Accept: application/json" \\
  -d @{postman_dir}/kongwen_request.json

## 4. 控温 - Protobuf请求
curl -X POST http://localhost:5000/api/v1/kongwen/predict \\
  -H "Content-Type: application/x-protobuf" \\
  -H "Accept: application/x-protobuf" \\
  --data-binary @{postman_dir}/kongwen_request.bin

## 5. 性能对比测试
# 测量响应时间和数据大小
curl -X POST http://localhost:5000/api/v1/shouwei/predict \\
  -H "Content-Type: application/json" \\
  -w "Time: %{{time_total}}s, Size: %{{size_download}} bytes\\n" \\
  -d @{postman_dir}/shouwei_request.json

curl -X POST http://localhost:5000/api/v1/shouwei/predict \\
  -H "Content-Type: application/x-protobuf" \\
  -w "Time: %{{time_total}}s, Size: %{{size_download}} bytes\\n" \\
  --data-binary @{postman_dir}/shouwei_request.bin
"""
    
    curl_file = postman_dir / "curl_examples.sh"
    with open(curl_file, 'w', encoding='utf-8') as f:
        f.write(curl_examples)
    print(f"✓ 生成curl示例: {curl_file}")

def generate_json_examples():
    """生成JSON示例文件"""
    postman_dir = Path(__file__).parent
    
    # 首尾控制JSON示例
    shouwei_json = {
        "device_info": {
            "device_id": "shouwei_device_001",
            "device_type": "shouwei_lasu_control",
            "location": "production_line_1"
        },
        "sensor_data": {
            "temperature": 25.5,
            "pressure": 1.2,
            "flow_rate": 15.8,
            "power_consumption": 850.0,
            "humidity": 65.2,
            "vibration": 0.05,
            "additional_sensors": {
                "oil_temperature": 45.2,
                "bearing_temperature": 38.5
            }
        },
        "control_parameters": {
            "target_temperature": 26.0,
            "max_power_limit": 1000,
            "control_precision": 0.1,
            "emergency_stop": False,
            "additional_params": {
                "pid_kp": 1.2,
                "pid_ki": 0.8,
                "pid_kd": 0.3
            }
        },
        "shouwei_data": {
            "lasu_coefficient": 1.25,
            "baseline_power": 500.0,
            "weight_corrections": [0.95, 1.02, 0.98, 1.01, 0.99],
            "power_distribution": {
                "main_ratio": 0.7,
                "vice_ratio": 0.25,
                "reserve_ratio": 0.05
            }
        },
        "timestamp": 1691234567890,
        "request_id": "req_shouwei_001"
    }
    
    # 控温JSON示例
    kongwen_json = {
        "device_info": {
            "device_id": "kongwen_device_001",
            "device_type": "kongwen_power_control",
            "location": "thermal_control_zone_A"
        },
        "sensor_data": {
            "temperature": 24.8,
            "pressure": 1.1,
            "flow_rate": 12.5,
            "power_consumption": 750.0,
            "humidity": 58.3,
            "vibration": 0.03,
            "additional_sensors": {
                "ambient_temperature": 20.0,
                "surface_temperature": 28.5
            }
        },
        "control_parameters": {
            "target_temperature": 25.5,
            "max_power_limit": 800,
            "control_precision": 0.05,
            "emergency_stop": False,
            "additional_params": {
                "heating_mode": "gradual",
                "cooling_mode": "active"
            }
        },
        "kongwen_data": {
            "thermal_capacity": 1000.0,
            "heat_transfer_coefficient": 10.0,
            "ambient_temperature": 20.0,
            "thermal_model": {
                "time_constant": 120.0,
                "gain": 0.85,
                "dead_time": 5.0,
                "pid_parameters": [1.5, 0.6, 0.2]
            }
        },
        "timestamp": 1691234567890,
        "request_id": "req_kongwen_001"
    }
    
    import json
    
    # 保存JSON文件
    shouwei_json_file = postman_dir / "shouwei_request.json"
    with open(shouwei_json_file, 'w', encoding='utf-8') as f:
        json.dump(shouwei_json, f, indent=2, ensure_ascii=False)
    print(f"✓ 生成首尾控制JSON示例: {shouwei_json_file}")
    
    kongwen_json_file = postman_dir / "kongwen_request.json"
    with open(kongwen_json_file, 'w', encoding='utf-8') as f:
        json.dump(kongwen_json, f, indent=2, ensure_ascii=False)
    print(f"✓ 生成控温JSON示例: {kongwen_json_file}")

def main():
    """主函数"""
    print("生成Postman测试数据")
    print("="*40)
    
    try:
        # 创建postman目录
        postman_dir = Path(__file__).parent
        postman_dir.mkdir(exist_ok=True)
        
        # 生成二进制Protobuf文件
        save_binary_files()
        
        # 生成JSON示例文件
        generate_json_examples()
        
        # 生成curl示例
        generate_curl_examples()
        
        print("\n🎉 测试数据生成完成！")
        print("\n📁 生成的文件:")
        print(f"  • {postman_dir}/shouwei_request.bin (Protobuf二进制)")
        print(f"  • {postman_dir}/kongwen_request.bin (Protobuf二进制)")
        print(f"  • {postman_dir}/shouwei_request.json (JSON示例)")
        print(f"  • {postman_dir}/kongwen_request.json (JSON示例)")
        print(f"  • {postman_dir}/curl_examples.sh (curl命令)")
        print(f"  • {postman_dir}/Industrial_Control_API_Tests.postman_collection.json (Postman集合)")
        
        print("\n🚀 使用方法:")
        print("1. 导入Postman集合文件到Postman")
        print("2. 设置环境变量 base_url = http://localhost:5000")
        print("3. 运行JSON测试请求")
        print("4. 上传.bin文件进行Protobuf测试")
        print("5. 对比JSON和Protobuf的响应时间和数据大小")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
