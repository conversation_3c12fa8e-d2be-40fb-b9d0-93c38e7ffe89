syntax = "proto3";

package industrial_control.v1;

import "industrial_control/v1/common.proto";
import "google/protobuf/timestamp.proto";

// 控温预测请求
message KongwenPredictionRequest {
    DeviceInfo device_info = 1;
    SensorData sensor_data = 2;
    ControlParameters control_parameters = 3;
    
    // 控温特有参数
    KongwenSpecificData kongwen_data = 4;
}

// 控温特有数据
message KongwenSpecificData {
    double thermal_capacity = 1;     // 热容量
    double heat_transfer_coefficient = 2; // 传热系数
    double ambient_temperature = 3;  // 环境温度
    ThermalModel thermal_model = 4;
}

// 热力学模型
message ThermalModel {
    double time_constant = 1;        // 时间常数
    double gain = 2;                 // 增益
    double dead_time = 3;            // 死区时间
    repeated double pid_parameters = 4; // PID参数 [Kp, Ki, Kd]
}

// 控温预测响应
message KongwenPredictionResponse {
    bool success = 1;
    string error_message = 2;
    PredictionResult prediction = 3;
    
    // 控温特有结果
    KongwenSpecificResult kongwen_result = 4;
}

// 控温特有结果
message KongwenSpecificResult {
    double required_heating_power = 1;
    double required_cooling_power = 2;
    double temperature_rise_rate = 3; // 升温速率
    double settling_time = 4;         // 稳定时间
    ThermalModel optimized_model = 5;
}

// 控温服务
service KongwenControlService {
    rpc Predict(KongwenPredictionRequest) returns (KongwenPredictionResponse);
    rpc GetThermalModel(DeviceInfo) returns (ThermalModelResponse);
    rpc OptimizePID(PIDOptimizationRequest) returns (PIDOptimizationResponse);
}

// 热力学模型响应
message ThermalModelResponse {
    bool success = 1;
    ThermalModel thermal_model = 2;
    double model_accuracy = 3;
    google.protobuf.Timestamp calibration_time = 4;
}

// PID优化请求
message PIDOptimizationRequest {
    DeviceInfo device_info = 1;
    ThermalModel current_model = 2;
    repeated double target_response = 3; // 目标响应曲线
}

// PID优化响应
message PIDOptimizationResponse {
    bool success = 1;
    string error_message = 2;
    repeated double optimized_pid = 3;
    double improvement_percentage = 4;
}
