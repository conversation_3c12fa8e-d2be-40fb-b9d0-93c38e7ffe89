# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: industrial_control/v1/shouwei_service.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'industrial_control/v1/shouwei_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from industrial_control.v1 import common_pb2 as industrial__control_dot_v1_dot_common__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+industrial_control/v1/shouwei_service.proto\x12\x15industrial_control.v1\x1a\"industrial_control/v1/common.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x92\x02\n\x18ShouweiPredictionRequest\x12\x36\n\x0b\x64\x65vice_info\x18\x01 \x01(\x0b\x32!.industrial_control.v1.DeviceInfo\x12\x36\n\x0bsensor_data\x18\x02 \x01(\x0b\x32!.industrial_control.v1.SensorData\x12\x44\n\x12\x63ontrol_parameters\x18\x03 \x01(\x0b\x32(.industrial_control.v1.ControlParameters\x12@\n\x0cshouwei_data\x18\x04 \x01(\x0b\x32*.industrial_control.v1.ShouweiSpecificData\"\xa9\x01\n\x13ShouweiSpecificData\x12\x18\n\x10lasu_coefficient\x18\x01 \x01(\x01\x12\x16\n\x0e\x62\x61seline_power\x18\x02 \x01(\x01\x12\x1a\n\x12weight_corrections\x18\x03 \x03(\x01\x12\x44\n\x12power_distribution\x18\x04 \x01(\x0b\x32(.industrial_control.v1.PowerDistribution\"R\n\x11PowerDistribution\x12\x12\n\nmain_ratio\x18\x01 \x01(\x01\x12\x12\n\nvice_ratio\x18\x02 \x01(\x01\x12\x15\n\rreserve_ratio\x18\x03 \x01(\x01\"\xc6\x01\n\x19ShouweiPredictionResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12;\n\nprediction\x18\x03 \x01(\x0b\x32\'.industrial_control.v1.PredictionResult\x12\x44\n\x0eshouwei_result\x18\x04 \x01(\x0b\x32,.industrial_control.v1.ShouweiSpecificResult\"\xad\x01\n\x15ShouweiSpecificResult\x12H\n\x16optimized_distribution\x18\x01 \x01(\x0b\x32(.industrial_control.v1.PowerDistribution\x12\x17\n\x0flasu_efficiency\x18\x02 \x01(\x01\x12\x18\n\x10\x61\x64justed_weights\x18\x03 \x03(\x01\x12\x17\n\x0fstability_index\x18\x04 \x01(\x01\"\xaa\x01\n\x13ModelStatusResponse\x12\x17\n\x0fmodel_available\x18\x01 \x01(\x08\x12\x15\n\rmodel_version\x18\x02 \x01(\t\x12/\n\x0blast_update\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x18\n\x10prediction_count\x18\x04 \x01(\x05\x12\x18\n\x10\x61verage_accuracy\x18\x05 \x01(\x01\"\x8d\x01\n\x12UpdateModelRequest\x12\x36\n\x0b\x64\x65vice_info\x18\x01 \x01(\x0b\x32!.industrial_control.v1.DeviceInfo\x12\x12\n\nmodel_data\x18\x02 \x01(\x0c\x12\x15\n\rmodel_version\x18\x03 \x01(\t\x12\x14\n\x0c\x66orce_update\x18\x04 \x01(\x08\"X\n\x13UpdateModelResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12\x19\n\x11new_model_version\x18\x03 \x01(\t2\xcc\x02\n\x15ShouweiControlService\x12l\n\x07Predict\x12/.industrial_control.v1.ShouweiPredictionRequest\x1a\x30.industrial_control.v1.ShouweiPredictionResponse\x12_\n\x0eGetModelStatus\x12!.industrial_control.v1.DeviceInfo\x1a*.industrial_control.v1.ModelStatusResponse\x12\x64\n\x0bUpdateModel\x12).industrial_control.v1.UpdateModelRequest\x1a*.industrial_control.v1.UpdateModelResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'industrial_control.v1.shouwei_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_SHOUWEIPREDICTIONREQUEST']._serialized_start=140
  _globals['_SHOUWEIPREDICTIONREQUEST']._serialized_end=414
  _globals['_SHOUWEISPECIFICDATA']._serialized_start=417
  _globals['_SHOUWEISPECIFICDATA']._serialized_end=586
  _globals['_POWERDISTRIBUTION']._serialized_start=588
  _globals['_POWERDISTRIBUTION']._serialized_end=670
  _globals['_SHOUWEIPREDICTIONRESPONSE']._serialized_start=673
  _globals['_SHOUWEIPREDICTIONRESPONSE']._serialized_end=871
  _globals['_SHOUWEISPECIFICRESULT']._serialized_start=874
  _globals['_SHOUWEISPECIFICRESULT']._serialized_end=1047
  _globals['_MODELSTATUSRESPONSE']._serialized_start=1050
  _globals['_MODELSTATUSRESPONSE']._serialized_end=1220
  _globals['_UPDATEMODELREQUEST']._serialized_start=1223
  _globals['_UPDATEMODELREQUEST']._serialized_end=1364
  _globals['_UPDATEMODELRESPONSE']._serialized_start=1366
  _globals['_UPDATEMODELRESPONSE']._serialized_end=1454
  _globals['_SHOUWEICONTROLSERVICE']._serialized_start=1457
  _globals['_SHOUWEICONTROLSERVICE']._serialized_end=1789
# @@protoc_insertion_point(module_scope)
