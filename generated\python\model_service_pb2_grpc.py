# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

import model_service_pb2 as model__service__pb2

GRPC_GENERATED_VERSION = '1.74.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in model_service_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class ModelServiceStub(object):
    """模型服务定义
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Predict = channel.unary_unary(
                '/bigdata.model.ModelService/Predict',
                request_serializer=model__service__pb2.PredictRequest.SerializeToString,
                response_deserializer=model__service__pb2.PredictResponse.FromString,
                _registered_method=True)
        self.BatchPredict = channel.unary_unary(
                '/bigdata.model.ModelService/BatchPredict',
                request_serializer=model__service__pb2.BatchPredictRequest.SerializeToString,
                response_deserializer=model__service__pb2.BatchPredictResponse.FromString,
                _registered_method=True)
        self.StreamPredict = channel.stream_stream(
                '/bigdata.model.ModelService/StreamPredict',
                request_serializer=model__service__pb2.PredictRequest.SerializeToString,
                response_deserializer=model__service__pb2.PredictResponse.FromString,
                _registered_method=True)
        self.SetupModel = channel.unary_unary(
                '/bigdata.model.ModelService/SetupModel',
                request_serializer=model__service__pb2.SetupModelRequest.SerializeToString,
                response_deserializer=model__service__pb2.SetupModelResponse.FromString,
                _registered_method=True)
        self.GetModelInfo = channel.unary_unary(
                '/bigdata.model.ModelService/GetModelInfo',
                request_serializer=model__service__pb2.GetModelInfoRequest.SerializeToString,
                response_deserializer=model__service__pb2.GetModelInfoResponse.FromString,
                _registered_method=True)
        self.HealthCheck = channel.unary_unary(
                '/bigdata.model.ModelService/HealthCheck',
                request_serializer=model__service__pb2.HealthCheckRequest.SerializeToString,
                response_deserializer=model__service__pb2.HealthCheckResponse.FromString,
                _registered_method=True)
        self.GetStats = channel.unary_unary(
                '/bigdata.model.ModelService/GetStats',
                request_serializer=model__service__pb2.GetStatsRequest.SerializeToString,
                response_deserializer=model__service__pb2.GetStatsResponse.FromString,
                _registered_method=True)


class ModelServiceServicer(object):
    """模型服务定义
    """

    def Predict(self, request, context):
        """模型预测
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BatchPredict(self, request, context):
        """批量预测
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StreamPredict(self, request_iterator, context):
        """流式预测
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetupModel(self, request, context):
        """模型设置
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetModelInfo(self, request, context):
        """获取模型信息
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def HealthCheck(self, request, context):
        """健康检查
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetStats(self, request, context):
        """获取性能统计
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ModelServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Predict': grpc.unary_unary_rpc_method_handler(
                    servicer.Predict,
                    request_deserializer=model__service__pb2.PredictRequest.FromString,
                    response_serializer=model__service__pb2.PredictResponse.SerializeToString,
            ),
            'BatchPredict': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchPredict,
                    request_deserializer=model__service__pb2.BatchPredictRequest.FromString,
                    response_serializer=model__service__pb2.BatchPredictResponse.SerializeToString,
            ),
            'StreamPredict': grpc.stream_stream_rpc_method_handler(
                    servicer.StreamPredict,
                    request_deserializer=model__service__pb2.PredictRequest.FromString,
                    response_serializer=model__service__pb2.PredictResponse.SerializeToString,
            ),
            'SetupModel': grpc.unary_unary_rpc_method_handler(
                    servicer.SetupModel,
                    request_deserializer=model__service__pb2.SetupModelRequest.FromString,
                    response_serializer=model__service__pb2.SetupModelResponse.SerializeToString,
            ),
            'GetModelInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.GetModelInfo,
                    request_deserializer=model__service__pb2.GetModelInfoRequest.FromString,
                    response_serializer=model__service__pb2.GetModelInfoResponse.SerializeToString,
            ),
            'HealthCheck': grpc.unary_unary_rpc_method_handler(
                    servicer.HealthCheck,
                    request_deserializer=model__service__pb2.HealthCheckRequest.FromString,
                    response_serializer=model__service__pb2.HealthCheckResponse.SerializeToString,
            ),
            'GetStats': grpc.unary_unary_rpc_method_handler(
                    servicer.GetStats,
                    request_deserializer=model__service__pb2.GetStatsRequest.FromString,
                    response_serializer=model__service__pb2.GetStatsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'bigdata.model.ModelService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('bigdata.model.ModelService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ModelService(object):
    """模型服务定义
    """

    @staticmethod
    def Predict(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/bigdata.model.ModelService/Predict',
            model__service__pb2.PredictRequest.SerializeToString,
            model__service__pb2.PredictResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BatchPredict(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/bigdata.model.ModelService/BatchPredict',
            model__service__pb2.BatchPredictRequest.SerializeToString,
            model__service__pb2.BatchPredictResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StreamPredict(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(
            request_iterator,
            target,
            '/bigdata.model.ModelService/StreamPredict',
            model__service__pb2.PredictRequest.SerializeToString,
            model__service__pb2.PredictResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetupModel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/bigdata.model.ModelService/SetupModel',
            model__service__pb2.SetupModelRequest.SerializeToString,
            model__service__pb2.SetupModelResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetModelInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/bigdata.model.ModelService/GetModelInfo',
            model__service__pb2.GetModelInfoRequest.SerializeToString,
            model__service__pb2.GetModelInfoResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def HealthCheck(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/bigdata.model.ModelService/HealthCheck',
            model__service__pb2.HealthCheckRequest.SerializeToString,
            model__service__pb2.HealthCheckResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetStats(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/bigdata.model.ModelService/GetStats',
            model__service__pb2.GetStatsRequest.SerializeToString,
            model__service__pb2.GetStatsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
