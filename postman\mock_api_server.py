#!/usr/bin/env python3
"""
模拟API服务器
用于测试Postman请求的简单Flask服务器
"""

import sys
import os
import time
import json
from flask import Flask, request, jsonify, Response
from flask_cors import CORS

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

app = Flask(__name__)
CORS(app)

# 模拟响应数据
def create_mock_response(device_type, protocol="json"):
    """创建模拟响应"""
    base_response = {
        "success": True,
        "prediction": {
            "main_power": 720.5,
            "vice_power": 180.2,
            "total_power": 900.7,
            "predicted_temperature": 26.1,
            "confidence": 0.95,
            "efficiency": 0.87
        },
        "metadata": {
            "protocol": protocol,
            "processing_time_ms": 25.3 if protocol == "protobuf" else 45.2,
            "model_version": "v1.0",
            "cache_hit": False,
            "timestamp": int(time.time() * 1000)
        }
    }
    
    if device_type == "shouwei":
        base_response["shouwei_result"] = {
            "lasu_efficiency": 0.92,
            "stability_index": 0.88,
            "adjusted_weights": [0.96, 1.01, 0.99, 1.00, 0.98],
            "optimized_distribution": {
                "main_ratio": 0.72,
                "vice_ratio": 0.23,
                "reserve_ratio": 0.05
            }
        }
    elif device_type == "kongwen":
        base_response["kongwen_result"] = {
            "required_heating_power": 450.2,
            "required_cooling_power": 120.8,
            "temperature_rise_rate": 0.15,
            "settling_time": 180.5,
            "thermal_efficiency": 0.89
        }
    
    return base_response

@app.route('/api/v1/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        "status": "healthy",
        "timestamp": int(time.time() * 1000),
        "version": "v1.0",
        "protocols": ["json", "protobuf"]
    })

@app.route('/api/v1/shouwei/predict', methods=['POST'])
def shouwei_predict():
    """首尾控制预测"""
    return handle_prediction_request("shouwei")

@app.route('/api/v1/kongwen/predict', methods=['POST'])
def kongwen_predict():
    """控温预测"""
    return handle_prediction_request("kongwen")

@app.route('/api/v1/predict', methods=['POST'])
def generic_predict():
    """通用预测（向后兼容）"""
    # 根据请求数据判断设备类型
    if request.is_json:
        data = request.get_json()
        device_type = data.get('device_type', 'generic')
        if 'shouwei' in device_type:
            return handle_prediction_request("shouwei")
        elif 'kongwen' in device_type:
            return handle_prediction_request("kongwen")
    
    return handle_prediction_request("generic")

def handle_prediction_request(device_type):
    """处理预测请求"""
    start_time = time.time()
    
    # 检测协议类型
    content_type = request.headers.get('Content-Type', '')
    is_protobuf = 'protobuf' in content_type
    
    try:
        if is_protobuf:
            # 处理Protobuf请求
            binary_data = request.get_data()
            print(f"收到Protobuf请求: {len(binary_data)} bytes")
            
            # 模拟Protobuf解析
            if len(binary_data) < 10:
                raise ValueError("Invalid protobuf data")
            
            # 创建Protobuf响应
            response_data = create_mock_response(device_type, "protobuf")
            
            # 模拟Protobuf序列化（实际应该使用真正的Protobuf）
            response_json = json.dumps(response_data)
            
            # 计算处理时间
            processing_time = (time.time() - start_time) * 1000
            response_data["metadata"]["processing_time_ms"] = processing_time
            
            return Response(
                response_json,
                mimetype='application/x-protobuf',
                headers={
                    'X-Protocol': 'protobuf',
                    'X-Processing-Time': f'{processing_time:.2f}ms',
                    'X-Data-Size': str(len(response_json))
                }
            )
        
        else:
            # 处理JSON请求
            if not request.is_json:
                return jsonify({
                    "success": False,
                    "error_message": "Invalid content type",
                    "error_code": 400
                }), 400
            
            data = request.get_json()
            print(f"收到JSON请求: {json.dumps(data, indent=2)}")
            
            # 验证必需字段
            if not data.get('device_info', {}).get('device_id'):
                return jsonify({
                    "success": False,
                    "error_message": "Missing device_id",
                    "error_code": 400
                }), 400
            
            # 创建JSON响应
            response_data = create_mock_response(device_type, "json")
            
            # 计算处理时间
            processing_time = (time.time() - start_time) * 1000
            response_data["metadata"]["processing_time_ms"] = processing_time
            
            return jsonify(response_data), 200, {
                'X-Protocol': 'json',
                'X-Processing-Time': f'{processing_time:.2f}ms',
                'X-Request-ID': data.get('request_id', 'unknown')
            }
    
    except Exception as e:
        return jsonify({
            "success": False,
            "error_message": str(e),
            "error_code": 500,
            "timestamp": int(time.time() * 1000)
        }), 500

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({
        "success": False,
        "error_message": "Endpoint not found",
        "error_code": 404,
        "available_endpoints": [
            "/api/v1/health",
            "/api/v1/shouwei/predict",
            "/api/v1/kongwen/predict",
            "/api/v1/predict"
        ]
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return jsonify({
        "success": False,
        "error_message": "Internal server error",
        "error_code": 500,
        "timestamp": int(time.time() * 1000)
    }), 500

if __name__ == '__main__':
    print("🚀 启动模拟API服务器")
    print("="*40)
    print("服务器地址: http://localhost:5000")
    print("支持的端点:")
    print("  GET  /api/v1/health")
    print("  POST /api/v1/shouwei/predict")
    print("  POST /api/v1/kongwen/predict")
    print("  POST /api/v1/predict")
    print()
    print("支持的协议:")
    print("  • JSON (application/json)")
    print("  • Protobuf (application/x-protobuf)")
    print()
    print("现在可以使用Postman进行测试！")
    print("="*40)
    
    app.run(host='0.0.0.0', port=5000, debug=True)
