#!/usr/bin/env python3
"""
gRPC模型服务实现
提供高性能的模型预测API
"""

import sys
import os
import time
import logging
import threading
from typing import Dict, Any, Iterator
from concurrent import futures

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import grpc
    from grpc_generated import model_service_pb2
    from grpc_generated import model_service_pb2_grpc
except ImportError:
    print("gRPC库未安装或代码未生成")
    print("请运行: python scripts/generate_grpc_code.py --install-deps --generate")
    sys.exit(1)

from DBUtil.Redis import RedisModelManager
from DBUtil.redis_pool_manager import get_redis_client
import main  # 导入现有的业务逻辑

logger = logging.getLogger(__name__)

class ModelServiceImpl(model_service_pb2_grpc.ModelServiceServicer):
    """模型服务gRPC实现"""
    
    def __init__(self):
        self.start_time = time.time()
        self.request_count = 0
        self.success_count = 0
        self.error_count = 0
        self.response_times = []
        self.model_stats = {}
        self.stats_lock = threading.RLock()
        
        # 初始化Redis管理器
        self.redis_manager = None
        self._initialize_redis()
    
    def _initialize_redis(self):
        """初始化Redis连接"""
        try:
            # 使用现有的配置
            import json
            with open("DBUtil/config.json", 'r') as f:
                config = json.load(f)
            
            redis_config = config.get("redis", {})
            self.redis_manager = RedisModelManager(
                redis_host=redis_config.get('host', 'localhost'),
                redis_port=redis_config.get('port', 6379),
                redis_password=redis_config.get('password', ''),
                redis_db=0
            )
            logger.info("gRPC服务Redis连接初始化成功")
        except Exception as e:
            logger.error(f"gRPC服务Redis连接初始化失败: {e}")
    
    def Predict(self, request, context):
        """单次预测"""
        start_time = time.time()
        
        try:
            with self.stats_lock:
                self.request_count += 1
            
            # 验证请求
            if not request.model_name or not request.device_id:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("model_name和device_id不能为空")
                return model_service_pb2.PredictResponse(
                    success=False,
                    error_message="model_name和device_id不能为空"
                )
            
            # 调用现有的预测逻辑
            prediction_result = self._call_existing_prediction_logic(
                request.model_name,
                request.device_id,
                request.version,
                dict(request.features)
            )
            
            processing_time = (time.time() - start_time) * 1000
            
            # 更新统计
            with self.stats_lock:
                self.success_count += 1
                self.response_times.append(processing_time)
                if len(self.response_times) > 1000:  # 保留最近1000次请求
                    self.response_times = self.response_times[-1000:]
                
                # 更新模型统计
                model_key = f"{request.model_name}:{request.device_id}"
                if model_key not in self.model_stats:
                    self.model_stats[model_key] = {'count': 0, 'total_time': 0.0}
                self.model_stats[model_key]['count'] += 1
                self.model_stats[model_key]['total_time'] += processing_time
            
            # 发布预测事件到流
            if self.redis_manager:
                self.redis_manager.publish_prediction_request(
                    request.model_name,
                    request.device_id,
                    dict(request.features),
                    prediction_result
                )
            
            return model_service_pb2.PredictResponse(
                success=True,
                predictions=prediction_result,
                processing_time_ms=processing_time,
                model_version=request.version,
                request_id=request.request_id,
                timestamp=int(time.time() * 1000)
            )
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            
            with self.stats_lock:
                self.error_count += 1
            
            logger.error(f"预测失败: {e}")
            
            return model_service_pb2.PredictResponse(
                success=False,
                error_message=str(e),
                processing_time_ms=processing_time,
                request_id=request.request_id,
                timestamp=int(time.time() * 1000)
            )
    
    def BatchPredict(self, request, context):
        """批量预测"""
        start_time = time.time()
        
        try:
            with self.stats_lock:
                self.request_count += len(request.batch_features)
            
            batch_predictions = []
            
            for features in request.batch_features:
                try:
                    prediction_result = self._call_existing_prediction_logic(
                        request.model_name,
                        request.device_id,
                        request.version,
                        dict(features)
                    )
                    batch_predictions.append(prediction_result)
                    
                    with self.stats_lock:
                        self.success_count += 1
                        
                except Exception as e:
                    logger.error(f"批量预测中的单次预测失败: {e}")
                    batch_predictions.append({})
                    
                    with self.stats_lock:
                        self.error_count += 1
            
            processing_time = (time.time() - start_time) * 1000
            
            return model_service_pb2.BatchPredictResponse(
                success=True,
                batch_predictions=batch_predictions,
                processing_time_ms=processing_time,
                model_version=request.version,
                request_id=request.request_id,
                timestamp=int(time.time() * 1000)
            )
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            logger.error(f"批量预测失败: {e}")
            
            return model_service_pb2.BatchPredictResponse(
                success=False,
                error_message=str(e),
                processing_time_ms=processing_time,
                request_id=request.request_id,
                timestamp=int(time.time() * 1000)
            )
    
    def StreamPredict(self, request_iterator, context):
        """流式预测"""
        for request in request_iterator:
            try:
                response = self.Predict(request, context)
                yield response
            except Exception as e:
                logger.error(f"流式预测失败: {e}")
                yield model_service_pb2.PredictResponse(
                    success=False,
                    error_message=str(e),
                    request_id=request.request_id,
                    timestamp=int(time.time() * 1000)
                )
    
    def SetupModel(self, request, context):
        """设置模型"""
        start_time = time.time()
        
        try:
            # 调用现有的模型设置逻辑
            baseline_values = self._call_existing_setup_logic(
                request.model_name,
                request.device_id,
                request.version,
                request.config,
                dict(request.parameters)
            )
            
            processing_time = (time.time() - start_time) * 1000
            
            return model_service_pb2.SetupModelResponse(
                success=True,
                baseline_values=baseline_values,
                processing_time_ms=processing_time,
                timestamp=int(time.time() * 1000)
            )
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            logger.error(f"模型设置失败: {e}")
            
            return model_service_pb2.SetupModelResponse(
                success=False,
                error_message=str(e),
                processing_time_ms=processing_time,
                timestamp=int(time.time() * 1000)
            )
    
    def GetModelInfo(self, request, context):
        """获取模型信息"""
        try:
            # 从Redis获取模型信息
            if self.redis_manager:
                model_data = self.redis_manager.load_model(
                    request.model_name,
                    request.device_id,
                    request.version
                )
                
                if model_data:
                    model_info = model_service_pb2.ModelInfo(
                        model_name=request.model_name,
                        device_id=request.device_id,
                        version=request.version,
                        model_type=model_data.get('model_type', 'unknown'),
                        last_updated=int(model_data.get('last_loaded', 0) * 1000),
                        last_accessed=int(time.time() * 1000),
                        access_count=0,  # 可以从统计中获取
                        model_size_mb=0.0,  # 可以计算模型大小
                        metadata={}
                    )
                    
                    return model_service_pb2.GetModelInfoResponse(
                        success=True,
                        model_info=model_info
                    )
            
            return model_service_pb2.GetModelInfoResponse(
                success=False,
                error_message="模型未找到"
            )
            
        except Exception as e:
            logger.error(f"获取模型信息失败: {e}")
            return model_service_pb2.GetModelInfoResponse(
                success=False,
                error_message=str(e)
            )
    
    def HealthCheck(self, request, context):
        """健康检查"""
        try:
            # 检查Redis连接
            if self.redis_manager:
                self.redis_manager.r.ping()
            
            return model_service_pb2.HealthCheckResponse(
                status=model_service_pb2.HealthCheckResponse.SERVING,
                message="服务正常",
                timestamp=int(time.time() * 1000)
            )
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return model_service_pb2.HealthCheckResponse(
                status=model_service_pb2.HealthCheckResponse.NOT_SERVING,
                message=str(e),
                timestamp=int(time.time() * 1000)
            )
    
    def GetStats(self, request, context):
        """获取统计信息"""
        try:
            with self.stats_lock:
                avg_response_time = sum(self.response_times) / len(self.response_times) if self.response_times else 0
                p95_response_time = sorted(self.response_times)[int(len(self.response_times) * 0.95)] if self.response_times else 0
                p99_response_time = sorted(self.response_times)[int(len(self.response_times) * 0.99)] if self.response_times else 0
                
                uptime = int(time.time() - self.start_time)
                cache_hit_rate = 0.0  # 可以从缓存管理器获取
                
                # 模型请求统计
                model_request_counts = {}
                model_avg_response_times = {}
                
                for model_key, stats in self.model_stats.items():
                    model_request_counts[model_key] = stats['count']
                    model_avg_response_times[model_key] = stats['total_time'] / stats['count']
                
                service_stats = model_service_pb2.ServiceStats(
                    total_requests=self.request_count,
                    successful_requests=self.success_count,
                    failed_requests=self.error_count,
                    avg_response_time_ms=avg_response_time,
                    p95_response_time_ms=p95_response_time,
                    p99_response_time_ms=p99_response_time,
                    active_models=len(self.model_stats),
                    cache_hit_rate=cache_hit_rate,
                    uptime_seconds=uptime,
                    model_request_counts=model_request_counts,
                    model_avg_response_times=model_avg_response_times
                )
                
                return model_service_pb2.GetStatsResponse(
                    success=True,
                    stats=service_stats
                )
                
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return model_service_pb2.GetStatsResponse(
                success=False,
                error_message=str(e)
            )
    
    def _call_existing_prediction_logic(self, model_name: str, device_id: str, version: str, features: Dict[str, float]) -> Dict[str, float]:
        """调用现有的预测逻辑"""
        # 这里需要根据具体的模型类型调用相应的预测函数
        # 例如：shouwei_lasu_control, kongwen_power_control等
        
        # 模拟调用现有逻辑
        if "shouwei" in model_name:
            # 调用首尾控制逻辑
            height = features.get('height', 0.0)
            weight = features.get('weight', 0.0)
            # 这里应该调用实际的shouwei预测函数
            return {'power': 100.0, 'speed': 2.5}  # 示例返回值
        
        elif "kongwen" in model_name:
            # 调用控温逻辑
            t = features.get('t', 0.0)
            ratio = features.get('ratio', 0.0)
            # 这里应该调用实际的kongwen预测函数
            return {'main_power': 50.0, 'vice_power': 30.0}  # 示例返回值
        
        else:
            raise ValueError(f"未知的模型类型: {model_name}")
    
    def _call_existing_setup_logic(self, model_name: str, device_id: str, version: str, config: Any, parameters: Dict[str, float]) -> Dict[str, float]:
        """调用现有的模型设置逻辑"""
        # 这里需要根据具体的模型类型调用相应的设置函数
        # 返回基线值
        return {'baseline_power': 100.0, 'baseline_speed': 2.5}  # 示例返回值

def serve(port: int = 50051, max_workers: int = 10):
    """启动gRPC服务器"""
    server = grpc.server(
        futures.ThreadPoolExecutor(max_workers=max_workers),
        options=[
            ('grpc.keepalive_time_ms', 30000),
            ('grpc.keepalive_timeout_ms', 5000),
            ('grpc.keepalive_permit_without_calls', True),
            ('grpc.http2.max_pings_without_data', 0),
            ('grpc.http2.min_time_between_pings_ms', 10000),
            ('grpc.http2.min_ping_interval_without_data_ms', 300000)
        ]
    )

    # 添加服务
    model_service_pb2_grpc.add_ModelServiceServicer_to_server(
        ModelServiceImpl(), server
    )

    # 监听端口
    listen_addr = f'[::]:{port}'
    server.add_insecure_port(listen_addr)

    # 启动服务器
    server.start()
    logger.info(f"gRPC服务器已启动，监听端口: {port}")

    try:
        server.wait_for_termination()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务器...")
        server.stop(grace=5)

if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description="gRPC模型服务")
    parser.add_argument("--port", type=int, default=50051, help="服务端口")
    parser.add_argument("--workers", type=int, default=10, help="工作线程数")

    args = parser.parse_args()

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    serve(args.port, args.workers)
