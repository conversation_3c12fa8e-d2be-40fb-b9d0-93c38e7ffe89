# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from industrial_control.v1 import common_pb2 as industrial__control_dot_v1_dot_common__pb2
from industrial_control.v1 import shouwei_service_pb2 as industrial__control_dot_v1_dot_shouwei__service__pb2

GRPC_GENERATED_VERSION = '1.74.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in industrial_control/v1/shouwei_service_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class ShouweiControlServiceStub(object):
    """首尾控制服务
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Predict = channel.unary_unary(
                '/industrial_control.v1.ShouweiControlService/Predict',
                request_serializer=industrial__control_dot_v1_dot_shouwei__service__pb2.ShouweiPredictionRequest.SerializeToString,
                response_deserializer=industrial__control_dot_v1_dot_shouwei__service__pb2.ShouweiPredictionResponse.FromString,
                _registered_method=True)
        self.GetModelStatus = channel.unary_unary(
                '/industrial_control.v1.ShouweiControlService/GetModelStatus',
                request_serializer=industrial__control_dot_v1_dot_common__pb2.DeviceInfo.SerializeToString,
                response_deserializer=industrial__control_dot_v1_dot_shouwei__service__pb2.ModelStatusResponse.FromString,
                _registered_method=True)
        self.UpdateModel = channel.unary_unary(
                '/industrial_control.v1.ShouweiControlService/UpdateModel',
                request_serializer=industrial__control_dot_v1_dot_shouwei__service__pb2.UpdateModelRequest.SerializeToString,
                response_deserializer=industrial__control_dot_v1_dot_shouwei__service__pb2.UpdateModelResponse.FromString,
                _registered_method=True)


class ShouweiControlServiceServicer(object):
    """首尾控制服务
    """

    def Predict(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetModelStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateModel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ShouweiControlServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Predict': grpc.unary_unary_rpc_method_handler(
                    servicer.Predict,
                    request_deserializer=industrial__control_dot_v1_dot_shouwei__service__pb2.ShouweiPredictionRequest.FromString,
                    response_serializer=industrial__control_dot_v1_dot_shouwei__service__pb2.ShouweiPredictionResponse.SerializeToString,
            ),
            'GetModelStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetModelStatus,
                    request_deserializer=industrial__control_dot_v1_dot_common__pb2.DeviceInfo.FromString,
                    response_serializer=industrial__control_dot_v1_dot_shouwei__service__pb2.ModelStatusResponse.SerializeToString,
            ),
            'UpdateModel': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateModel,
                    request_deserializer=industrial__control_dot_v1_dot_shouwei__service__pb2.UpdateModelRequest.FromString,
                    response_serializer=industrial__control_dot_v1_dot_shouwei__service__pb2.UpdateModelResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'industrial_control.v1.ShouweiControlService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('industrial_control.v1.ShouweiControlService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ShouweiControlService(object):
    """首尾控制服务
    """

    @staticmethod
    def Predict(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/industrial_control.v1.ShouweiControlService/Predict',
            industrial__control_dot_v1_dot_shouwei__service__pb2.ShouweiPredictionRequest.SerializeToString,
            industrial__control_dot_v1_dot_shouwei__service__pb2.ShouweiPredictionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetModelStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/industrial_control.v1.ShouweiControlService/GetModelStatus',
            industrial__control_dot_v1_dot_common__pb2.DeviceInfo.SerializeToString,
            industrial__control_dot_v1_dot_shouwei__service__pb2.ModelStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateModel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/industrial_control.v1.ShouweiControlService/UpdateModel',
            industrial__control_dot_v1_dot_shouwei__service__pb2.UpdateModelRequest.SerializeToString,
            industrial__control_dot_v1_dot_shouwei__service__pb2.UpdateModelResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
