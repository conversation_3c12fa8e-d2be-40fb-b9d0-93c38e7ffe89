# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: model_service.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'model_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13model_service.proto\x12\rbigdata.model\"\xdf\x01\n\x0ePredictRequest\x12\x12\n\nmodel_name\x18\x01 \x01(\t\x12\x11\n\tdevice_id\x18\x02 \x01(\t\x12\x0f\n\x07version\x18\x03 \x01(\t\x12=\n\x08\x66\x65\x61tures\x18\x04 \x03(\x0b\x32+.bigdata.model.PredictRequest.FeaturesEntry\x12\x11\n\ttimestamp\x18\x05 \x01(\x03\x12\x12\n\nrequest_id\x18\x06 \x01(\t\x1a/\n\rFeaturesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01:\x02\x38\x01\"\x8d\x02\n\x0fPredictResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x44\n\x0bpredictions\x18\x02 \x03(\x0b\x32/.bigdata.model.PredictResponse.PredictionsEntry\x12\x15\n\rerror_message\x18\x03 \x01(\t\x12\x1a\n\x12processing_time_ms\x18\x04 \x01(\x01\x12\x15\n\rmodel_version\x18\x05 \x01(\t\x12\x12\n\nrequest_id\x18\x06 \x01(\t\x12\x11\n\ttimestamp\x18\x07 \x01(\x03\x1a\x32\n\x10PredictionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01:\x02\x38\x01\"\xa7\x01\n\x13\x42\x61tchPredictRequest\x12\x12\n\nmodel_name\x18\x01 \x01(\t\x12\x11\n\tdevice_id\x18\x02 \x01(\t\x12\x0f\n\x07version\x18\x03 \x01(\t\x12\x31\n\x0e\x62\x61tch_features\x18\x04 \x03(\x0b\x32\x19.bigdata.model.FeatureMap\x12\x11\n\ttimestamp\x18\x05 \x01(\x03\x12\x12\n\nrequest_id\x18\x06 \x01(\t\"x\n\nFeatureMap\x12\x39\n\x08\x66\x65\x61tures\x18\x01 \x03(\x0b\x32\'.bigdata.model.FeatureMap.FeaturesEntry\x1a/\n\rFeaturesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01:\x02\x38\x01\"\xd1\x01\n\x14\x42\x61tchPredictResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x37\n\x11\x62\x61tch_predictions\x18\x02 \x03(\x0b\x32\x1c.bigdata.model.PredictionMap\x12\x15\n\rerror_message\x18\x03 \x01(\t\x12\x1a\n\x12processing_time_ms\x18\x04 \x01(\x01\x12\x15\n\rmodel_version\x18\x05 \x01(\t\x12\x12\n\nrequest_id\x18\x06 \x01(\t\x12\x11\n\ttimestamp\x18\x07 \x01(\x03\"\x87\x01\n\rPredictionMap\x12\x42\n\x0bpredictions\x18\x01 \x03(\x0b\x32-.bigdata.model.PredictionMap.PredictionsEntry\x1a\x32\n\x10PredictionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01:\x02\x38\x01\"\x83\x02\n\x11SetupModelRequest\x12\x12\n\nmodel_name\x18\x01 \x01(\t\x12\x11\n\tdevice_id\x18\x02 \x01(\t\x12\x0f\n\x07version\x18\x03 \x01(\t\x12*\n\x06\x63onfig\x18\x04 \x01(\x0b\x32\x1a.bigdata.model.ModelConfig\x12\x44\n\nparameters\x18\x05 \x03(\x0b\x32\x30.bigdata.model.SetupModelRequest.ParametersEntry\x12\x11\n\ttimestamp\x18\x06 \x01(\x03\x1a\x31\n\x0fParametersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01:\x02\x38\x01\"\x98\x02\n\x0bModelConfig\x12\x14\n\x0cproduct_type\x18\x01 \x01(\t\x12\x12\n\nfield_size\x18\x02 \x01(\t\x12\r\n\x05power\x18\x03 \x01(\x01\x12\x10\n\x08\x64iameter\x18\x04 \x01(\x01\x12\x0e\n\x06weight\x18\x05 \x01(\x01\x12\x12\n\ntarget_dia\x18\x06 \x03(\x01\x12\x14\n\x0ctarget_power\x18\x07 \x03(\x01\x12K\n\x11\x61\x64\x64itional_config\x18\x08 \x03(\x0b\x32\x30.bigdata.model.ModelConfig.AdditionalConfigEntry\x1a\x37\n\x15\x41\x64\x64itionalConfigEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xf2\x01\n\x12SetupModelResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12N\n\x0f\x62\x61seline_values\x18\x03 \x03(\x0b\x32\x35.bigdata.model.SetupModelResponse.BaselineValuesEntry\x12\x1a\n\x12processing_time_ms\x18\x04 \x01(\x01\x12\x11\n\ttimestamp\x18\x05 \x01(\x03\x1a\x35\n\x13\x42\x61selineValuesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01:\x02\x38\x01\"M\n\x13GetModelInfoRequest\x12\x12\n\nmodel_name\x18\x01 \x01(\t\x12\x11\n\tdevice_id\x18\x02 \x01(\t\x12\x0f\n\x07version\x18\x03 \x01(\t\"l\n\x14GetModelInfoResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12,\n\nmodel_info\x18\x03 \x01(\x0b\x32\x18.bigdata.model.ModelInfo\"\x9c\x02\n\tModelInfo\x12\x12\n\nmodel_name\x18\x01 \x01(\t\x12\x11\n\tdevice_id\x18\x02 \x01(\t\x12\x0f\n\x07version\x18\x03 \x01(\t\x12\x12\n\nmodel_type\x18\x04 \x01(\t\x12\x14\n\x0clast_updated\x18\x05 \x01(\x03\x12\x15\n\rlast_accessed\x18\x06 \x01(\x03\x12\x14\n\x0c\x61\x63\x63\x65ss_count\x18\x07 \x01(\x05\x12\x15\n\rmodel_size_mb\x18\x08 \x01(\x01\x12\x38\n\x08metadata\x18\t \x03(\x0b\x32&.bigdata.model.ModelInfo.MetadataEntry\x1a/\n\rMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"%\n\x12HealthCheckRequest\x12\x0f\n\x07service\x18\x01 \x01(\t\"\xcc\x01\n\x13HealthCheckResponse\x12@\n\x06status\x18\x01 \x01(\x0e\x32\x30.bigdata.model.HealthCheckResponse.ServingStatus\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x11\n\ttimestamp\x18\x03 \x01(\x03\"O\n\rServingStatus\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x0b\n\x07SERVING\x10\x01\x12\x0f\n\x0bNOT_SERVING\x10\x02\x12\x13\n\x0fSERVICE_UNKNOWN\x10\x03\"]\n\x0fGetStatsRequest\x12\x18\n\x10include_detailed\x18\x01 \x01(\x08\x12\x18\n\x10time_range_start\x18\x02 \x01(\x03\x12\x16\n\x0etime_range_end\x18\x03 \x01(\x03\"f\n\x10GetStatsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12*\n\x05stats\x18\x03 \x01(\x0b\x32\x1b.bigdata.model.ServiceStats\"\xa3\x04\n\x0cServiceStats\x12\x16\n\x0etotal_requests\x18\x01 \x01(\x03\x12\x1b\n\x13successful_requests\x18\x02 \x01(\x03\x12\x17\n\x0f\x66\x61iled_requests\x18\x03 \x01(\x03\x12\x1c\n\x14\x61vg_response_time_ms\x18\x04 \x01(\x01\x12\x1c\n\x14p95_response_time_ms\x18\x05 \x01(\x01\x12\x1c\n\x14p99_response_time_ms\x18\x06 \x01(\x01\x12\x15\n\ractive_models\x18\x07 \x01(\x05\x12\x16\n\x0e\x63\x61\x63he_hit_rate\x18\x08 \x01(\x01\x12\x16\n\x0euptime_seconds\x18\t \x01(\x03\x12Q\n\x14model_request_counts\x18\n \x03(\x0b\x32\x33.bigdata.model.ServiceStats.ModelRequestCountsEntry\x12X\n\x18model_avg_response_times\x18\x0b \x03(\x0b\x32\x36.bigdata.model.ServiceStats.ModelAvgResponseTimesEntry\x1a\x39\n\x17ModelRequestCountsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x03:\x02\x38\x01\x1a<\n\x1aModelAvgResponseTimesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01:\x02\x38\x01\x32\xd4\x04\n\x0cModelService\x12H\n\x07Predict\x12\x1d.bigdata.model.PredictRequest\x1a\x1e.bigdata.model.PredictResponse\x12W\n\x0c\x42\x61tchPredict\x12\".bigdata.model.BatchPredictRequest\x1a#.bigdata.model.BatchPredictResponse\x12R\n\rStreamPredict\x12\x1d.bigdata.model.PredictRequest\x1a\x1e.bigdata.model.PredictResponse(\x01\x30\x01\x12Q\n\nSetupModel\x12 .bigdata.model.SetupModelRequest\x1a!.bigdata.model.SetupModelResponse\x12W\n\x0cGetModelInfo\x12\".bigdata.model.GetModelInfoRequest\x1a#.bigdata.model.GetModelInfoResponse\x12T\n\x0bHealthCheck\x12!.bigdata.model.HealthCheckRequest\x1a\".bigdata.model.HealthCheckResponse\x12K\n\x08GetStats\x12\x1e.bigdata.model.GetStatsRequest\x1a\x1f.bigdata.model.GetStatsResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'model_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_PREDICTREQUEST_FEATURESENTRY']._loaded_options = None
  _globals['_PREDICTREQUEST_FEATURESENTRY']._serialized_options = b'8\001'
  _globals['_PREDICTRESPONSE_PREDICTIONSENTRY']._loaded_options = None
  _globals['_PREDICTRESPONSE_PREDICTIONSENTRY']._serialized_options = b'8\001'
  _globals['_FEATUREMAP_FEATURESENTRY']._loaded_options = None
  _globals['_FEATUREMAP_FEATURESENTRY']._serialized_options = b'8\001'
  _globals['_PREDICTIONMAP_PREDICTIONSENTRY']._loaded_options = None
  _globals['_PREDICTIONMAP_PREDICTIONSENTRY']._serialized_options = b'8\001'
  _globals['_SETUPMODELREQUEST_PARAMETERSENTRY']._loaded_options = None
  _globals['_SETUPMODELREQUEST_PARAMETERSENTRY']._serialized_options = b'8\001'
  _globals['_MODELCONFIG_ADDITIONALCONFIGENTRY']._loaded_options = None
  _globals['_MODELCONFIG_ADDITIONALCONFIGENTRY']._serialized_options = b'8\001'
  _globals['_SETUPMODELRESPONSE_BASELINEVALUESENTRY']._loaded_options = None
  _globals['_SETUPMODELRESPONSE_BASELINEVALUESENTRY']._serialized_options = b'8\001'
  _globals['_MODELINFO_METADATAENTRY']._loaded_options = None
  _globals['_MODELINFO_METADATAENTRY']._serialized_options = b'8\001'
  _globals['_SERVICESTATS_MODELREQUESTCOUNTSENTRY']._loaded_options = None
  _globals['_SERVICESTATS_MODELREQUESTCOUNTSENTRY']._serialized_options = b'8\001'
  _globals['_SERVICESTATS_MODELAVGRESPONSETIMESENTRY']._loaded_options = None
  _globals['_SERVICESTATS_MODELAVGRESPONSETIMESENTRY']._serialized_options = b'8\001'
  _globals['_PREDICTREQUEST']._serialized_start=39
  _globals['_PREDICTREQUEST']._serialized_end=262
  _globals['_PREDICTREQUEST_FEATURESENTRY']._serialized_start=215
  _globals['_PREDICTREQUEST_FEATURESENTRY']._serialized_end=262
  _globals['_PREDICTRESPONSE']._serialized_start=265
  _globals['_PREDICTRESPONSE']._serialized_end=534
  _globals['_PREDICTRESPONSE_PREDICTIONSENTRY']._serialized_start=484
  _globals['_PREDICTRESPONSE_PREDICTIONSENTRY']._serialized_end=534
  _globals['_BATCHPREDICTREQUEST']._serialized_start=537
  _globals['_BATCHPREDICTREQUEST']._serialized_end=704
  _globals['_FEATUREMAP']._serialized_start=706
  _globals['_FEATUREMAP']._serialized_end=826
  _globals['_FEATUREMAP_FEATURESENTRY']._serialized_start=215
  _globals['_FEATUREMAP_FEATURESENTRY']._serialized_end=262
  _globals['_BATCHPREDICTRESPONSE']._serialized_start=829
  _globals['_BATCHPREDICTRESPONSE']._serialized_end=1038
  _globals['_PREDICTIONMAP']._serialized_start=1041
  _globals['_PREDICTIONMAP']._serialized_end=1176
  _globals['_PREDICTIONMAP_PREDICTIONSENTRY']._serialized_start=484
  _globals['_PREDICTIONMAP_PREDICTIONSENTRY']._serialized_end=534
  _globals['_SETUPMODELREQUEST']._serialized_start=1179
  _globals['_SETUPMODELREQUEST']._serialized_end=1438
  _globals['_SETUPMODELREQUEST_PARAMETERSENTRY']._serialized_start=1389
  _globals['_SETUPMODELREQUEST_PARAMETERSENTRY']._serialized_end=1438
  _globals['_MODELCONFIG']._serialized_start=1441
  _globals['_MODELCONFIG']._serialized_end=1721
  _globals['_MODELCONFIG_ADDITIONALCONFIGENTRY']._serialized_start=1666
  _globals['_MODELCONFIG_ADDITIONALCONFIGENTRY']._serialized_end=1721
  _globals['_SETUPMODELRESPONSE']._serialized_start=1724
  _globals['_SETUPMODELRESPONSE']._serialized_end=1966
  _globals['_SETUPMODELRESPONSE_BASELINEVALUESENTRY']._serialized_start=1913
  _globals['_SETUPMODELRESPONSE_BASELINEVALUESENTRY']._serialized_end=1966
  _globals['_GETMODELINFOREQUEST']._serialized_start=1968
  _globals['_GETMODELINFOREQUEST']._serialized_end=2045
  _globals['_GETMODELINFORESPONSE']._serialized_start=2047
  _globals['_GETMODELINFORESPONSE']._serialized_end=2155
  _globals['_MODELINFO']._serialized_start=2158
  _globals['_MODELINFO']._serialized_end=2442
  _globals['_MODELINFO_METADATAENTRY']._serialized_start=2395
  _globals['_MODELINFO_METADATAENTRY']._serialized_end=2442
  _globals['_HEALTHCHECKREQUEST']._serialized_start=2444
  _globals['_HEALTHCHECKREQUEST']._serialized_end=2481
  _globals['_HEALTHCHECKRESPONSE']._serialized_start=2484
  _globals['_HEALTHCHECKRESPONSE']._serialized_end=2688
  _globals['_HEALTHCHECKRESPONSE_SERVINGSTATUS']._serialized_start=2609
  _globals['_HEALTHCHECKRESPONSE_SERVINGSTATUS']._serialized_end=2688
  _globals['_GETSTATSREQUEST']._serialized_start=2690
  _globals['_GETSTATSREQUEST']._serialized_end=2783
  _globals['_GETSTATSRESPONSE']._serialized_start=2785
  _globals['_GETSTATSRESPONSE']._serialized_end=2887
  _globals['_SERVICESTATS']._serialized_start=2890
  _globals['_SERVICESTATS']._serialized_end=3437
  _globals['_SERVICESTATS_MODELREQUESTCOUNTSENTRY']._serialized_start=3318
  _globals['_SERVICESTATS_MODELREQUESTCOUNTSENTRY']._serialized_end=3375
  _globals['_SERVICESTATS_MODELAVGRESPONSETIMESENTRY']._serialized_start=3377
  _globals['_SERVICESTATS_MODELAVGRESPONSETIMESENTRY']._serialized_end=3437
  _globals['_MODELSERVICE']._serialized_start=3440
  _globals['_MODELSERVICE']._serialized_end=4036
# @@protoc_insertion_point(module_scope)
