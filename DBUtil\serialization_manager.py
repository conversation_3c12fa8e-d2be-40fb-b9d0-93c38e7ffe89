#!/usr/bin/env python3
"""
序列化管理器 - 高性能序列化解决方案
支持MessagePack + LZ4压缩，自动格式检测，性能统计
"""

import pickle
import logging
import time
from typing import Any, Dict, Optional
from enum import Enum

# 尝试导入优化库，如果不存在则降级到基础实现
try:
    import msgpack
    import lz4.frame
    OPTIMIZATION_AVAILABLE = True
except ImportError:
    OPTIMIZATION_AVAILABLE = False
    logging.warning("优化库未安装，将使用Pickle作为降级方案")

# 尝试导入Protobuf库
try:
    import google.protobuf.message
    from google.protobuf.json_format import MessageToDict, Parse
    PROTOBUF_AVAILABLE = True
except ImportError:
    PROTOBUF_AVAILABLE = False
    logging.warning("Protobuf库未安装，Protobuf功能不可用")

logger = logging.getLogger(__name__)

class SerializationMethod(Enum):
    """序列化方法枚举"""
    PICKLE = "pickle"
    MSGPACK = "msgpack"
    MSGPACK_LZ4 = "msgpack_lz4"
    PROTOBUF = "protobuf"
    PROTOBUF_LZ4 = "protobuf_lz4"

class SerializationManager:
    """
    高性能序列化管理器
    支持MessagePack + LZ4压缩，自动格式检测，性能统计
    """

    def __init__(self,
                 default_method: SerializationMethod = SerializationMethod.MSGPACK_LZ4,
                 enable_fallback: bool = True,
                 enable_stats: bool = True):
        """
        初始化序列化管理器

        Args:
            default_method: 默认序列化方法
            enable_fallback: 是否启用降级机制
            enable_stats: 是否启用性能统计
        """
        self.default_method = default_method
        self.enable_fallback = enable_fallback
        self.enable_stats = enable_stats

        # 性能统计
        self.stats = {
            'serialize_count': 0,
            'deserialize_count': 0,
            'serialize_time_total': 0.0,
            'deserialize_time_total': 0.0,
            'compression_ratio_total': 0.0,
            'fallback_count': 0,
            'error_count': 0
        }

        # 检查优化库可用性
        if not OPTIMIZATION_AVAILABLE and default_method != SerializationMethod.PICKLE:
            logger.info("优化库不可用，降级到Pickle方案")
            self.default_method = SerializationMethod.PICKLE
    
    def _detect_format(self, data: bytes) -> SerializationMethod:
        """
        自动检测数据的序列化格式

        Args:
            data: 序列化后的字节数据

        Returns:
            检测到的序列化方法
        """
        if not data:
            return SerializationMethod.PICKLE

        # LZ4压缩数据的魔数检测
        if data.startswith(b'\x04"M\x18'):
            return SerializationMethod.MSGPACK_LZ4

        # MessagePack格式检测
        if OPTIMIZATION_AVAILABLE:
            try:
                msgpack.unpackb(data[:min(100, len(data))], raw=False)
                return SerializationMethod.MSGPACK
            except:
                pass

        # 默认认为是Pickle格式
        return SerializationMethod.PICKLE
    
    def serialize(self, data: Any, method: Optional[SerializationMethod] = None) -> bytes:
        """
        序列化数据
        
        Args:
            data: 要序列化的数据
            method: 指定的序列化方法，None则使用默认方法
            
        Returns:
            序列化后的字节数据
        """
        start_time = time.time()
        method = method or self.default_method
        
        try:
            if method == SerializationMethod.MSGPACK_LZ4 and OPTIMIZATION_AVAILABLE:
                result = self._serialize_msgpack_lz4(data)
            elif method == SerializationMethod.MSGPACK and OPTIMIZATION_AVAILABLE:
                result = self._serialize_msgpack(data)
            else:
                result = self._serialize_pickle(data)
            
            # 更新统计信息
            if self.enable_stats:
                serialize_time = time.time() - start_time
                self.stats['serialize_count'] += 1
                self.stats['serialize_time_total'] += serialize_time

                # 计算压缩比（相对于Pickle）
                if method != SerializationMethod.PICKLE:
                    try:
                        pickle_size = len(pickle.dumps(data))
                        compression_ratio = len(result) / pickle_size
                        self.stats['compression_ratio_total'] += compression_ratio
                    except:
                        pass  # 忽略压缩比计算错误

            return result
            
        except Exception:
            self.stats['error_count'] += 1

            # 降级处理
            if self.enable_fallback and method != SerializationMethod.PICKLE:
                self.stats['fallback_count'] += 1
                return self._serialize_pickle(data)
            else:
                raise
    
    def deserialize(self, data: bytes, method: Optional[SerializationMethod] = None) -> Any:
        """
        反序列化数据
        
        Args:
            data: 序列化后的字节数据
            method: 指定的序列化方法，None则自动检测
            
        Returns:
            反序列化后的数据
        """
        start_time = time.time()
        
        # 自动检测格式
        if method is None:
            method = self._detect_format(data)
        
        try:
            if method == SerializationMethod.MSGPACK_LZ4 and OPTIMIZATION_AVAILABLE:
                result = self._deserialize_msgpack_lz4(data)
            elif method == SerializationMethod.MSGPACK and OPTIMIZATION_AVAILABLE:
                result = self._deserialize_msgpack(data)
            else:
                result = self._deserialize_pickle(data)
            
            # 更新统计信息
            if self.enable_stats:
                deserialize_time = time.time() - start_time
                self.stats['deserialize_count'] += 1
                self.stats['deserialize_time_total'] += deserialize_time

            return result

        except Exception:
            self.stats['error_count'] += 1

            # 降级处理
            if self.enable_fallback and method != SerializationMethod.PICKLE:
                self.stats['fallback_count'] += 1
                return self._deserialize_pickle(data)
            else:
                raise
    
    def _serialize_pickle(self, data: Any) -> bytes:
        """Pickle序列化"""
        return pickle.dumps(data, protocol=pickle.HIGHEST_PROTOCOL)
    
    def _deserialize_pickle(self, data: bytes) -> Any:
        """Pickle反序列化"""
        return pickle.loads(data)
    
    def _serialize_msgpack(self, data: Any) -> bytes:
        """MessagePack序列化"""
        return msgpack.packb(data, use_bin_type=True)
    
    def _deserialize_msgpack(self, data: bytes) -> Any:
        """MessagePack反序列化"""
        return msgpack.unpackb(data, raw=False)
    
    def _serialize_msgpack_lz4(self, data: Any) -> bytes:
        """MessagePack + LZ4压缩序列化"""
        serialized = msgpack.packb(data, use_bin_type=True)
        return lz4.frame.compress(serialized)
    
    def _deserialize_msgpack_lz4(self, data: bytes) -> Any:
        """MessagePack + LZ4解压缩反序列化"""
        decompressed = lz4.frame.decompress(data)
        return msgpack.unpackb(decompressed, raw=False)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        if not self.enable_stats or self.stats['serialize_count'] == 0:
            return {}
        
        return {
            'serialize_count': self.stats['serialize_count'],
            'deserialize_count': self.stats['deserialize_count'],
            'avg_serialize_time_ms': (self.stats['serialize_time_total'] / self.stats['serialize_count']) * 1000,
            'avg_deserialize_time_ms': (self.stats['deserialize_time_total'] / self.stats['deserialize_count']) * 1000,
            'avg_compression_ratio': self.stats['compression_ratio_total'] / max(1, self.stats['serialize_count']),
            'fallback_rate': self.stats['fallback_count'] / max(1, self.stats['serialize_count']),
            'error_rate': self.stats['error_count'] / max(1, self.stats['serialize_count'] + self.stats['deserialize_count'])
        }
    
    def reset_stats(self):
        """重置统计信息"""
        for key in self.stats:
            self.stats[key] = 0

# 全局序列化管理器实例
serialization_manager = SerializationManager()
