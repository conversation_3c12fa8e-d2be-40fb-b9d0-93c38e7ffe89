# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# 这是一个模拟的protobuf模块，用于演示集成

from .common_pb2 import DeviceInfo, SensorData, ControlParameters, PredictionResult

class ShouweiSpecificData:
    def __init__(self):
        self.lasu_coefficient = 0.0
        self.baseline_power = 0.0
        self.weight_corrections = []
        self.power_distribution = PowerDistribution()

class PowerDistribution:
    def __init__(self):
        self.main_ratio = 0.0
        self.vice_ratio = 0.0
        self.reserve_ratio = 0.0

class ShouweiPredictionRequest:
    def __init__(self):
        self.device_info = DeviceInfo()
        self.sensor_data = SensorData()
        self.control_parameters = ControlParameters()
        self.shouwei_data = ShouweiSpecificData()
    
    def SerializeToString(self):
        """模拟序列化方法"""
        import json
        data = {
            'device_info': {
                'device_id': self.device_info.device_id,
                'device_type': self.device_info.device_type,
                'location': self.device_info.location
            },
            'sensor_data': {
                'temperature': self.sensor_data.temperature,
                'pressure': self.sensor_data.pressure,
                'flow_rate': self.sensor_data.flow_rate,
                'power_consumption': self.sensor_data.power_consumption
            },
            'control_parameters': {
                'target_temperature': self.control_parameters.target_temperature,
                'max_power_limit': self.control_parameters.max_power_limit,
                'emergency_stop': self.control_parameters.emergency_stop
            },
            'shouwei_data': {
                'lasu_coefficient': self.shouwei_data.lasu_coefficient,
                'baseline_power': self.shouwei_data.baseline_power,
                'weight_corrections': self.shouwei_data.weight_corrections
            }
        }
        return json.dumps(data).encode('utf-8')
    
    def ParseFromString(self, data):
        """模拟反序列化方法"""
        import json
        parsed = json.loads(data.decode('utf-8'))
        
        # 填充设备信息
        device_info = parsed.get('device_info', {})
        self.device_info.device_id = device_info.get('device_id', '')
        self.device_info.device_type = device_info.get('device_type', '')
        self.device_info.location = device_info.get('location', '')
        
        # 填充传感器数据
        sensor_data = parsed.get('sensor_data', {})
        self.sensor_data.temperature = sensor_data.get('temperature', 0.0)
        self.sensor_data.pressure = sensor_data.get('pressure', 0.0)
        self.sensor_data.flow_rate = sensor_data.get('flow_rate', 0.0)
        self.sensor_data.power_consumption = sensor_data.get('power_consumption', 0.0)
        
        # 填充控制参数
        control_params = parsed.get('control_parameters', {})
        self.control_parameters.target_temperature = control_params.get('target_temperature', 0.0)
        self.control_parameters.max_power_limit = control_params.get('max_power_limit', 0)
        self.control_parameters.emergency_stop = control_params.get('emergency_stop', False)
        
        # 填充首尾控制特有数据
        shouwei_data = parsed.get('shouwei_data', {})
        self.shouwei_data.lasu_coefficient = shouwei_data.get('lasu_coefficient', 0.0)
        self.shouwei_data.baseline_power = shouwei_data.get('baseline_power', 0.0)
        self.shouwei_data.weight_corrections = shouwei_data.get('weight_corrections', [])

class ShouweiSpecificResult:
    def __init__(self):
        self.optimized_distribution = PowerDistribution()
        self.lasu_efficiency = 0.0
        self.adjusted_weights = []
        self.stability_index = 0.0

class ShouweiPredictionResponse:
    def __init__(self):
        self.success = False
        self.error_message = ""
        self.prediction = PredictionResult()
        self.shouwei_result = ShouweiSpecificResult()
    
    def SerializeToString(self):
        """模拟序列化方法"""
        import json
        data = {
            'success': self.success,
            'error_message': self.error_message,
            'prediction': {
                'main_power': self.prediction.main_power,
                'vice_power': self.prediction.vice_power,
                'total_power': self.prediction.total_power,
                'predicted_temperature': self.prediction.predicted_temperature,
                'confidence': self.prediction.confidence
            },
            'shouwei_result': {
                'lasu_efficiency': self.shouwei_result.lasu_efficiency,
                'stability_index': self.shouwei_result.stability_index,
                'adjusted_weights': self.shouwei_result.adjusted_weights
            }
        }
        return json.dumps(data).encode('utf-8')
    
    def ParseFromString(self, data):
        """模拟反序列化方法"""
        import json
        parsed = json.loads(data.decode('utf-8'))
        self.success = parsed.get('success', False)
        self.error_message = parsed.get('error_message', '')
        
        # 填充预测结果
        prediction = parsed.get('prediction', {})
        self.prediction.main_power = prediction.get('main_power', 0.0)
        self.prediction.vice_power = prediction.get('vice_power', 0.0)
        self.prediction.total_power = prediction.get('total_power', 0.0)
        self.prediction.predicted_temperature = prediction.get('predicted_temperature', 0.0)
        self.prediction.confidence = prediction.get('confidence', 0.0)
        
        # 填充首尾控制特有结果
        shouwei_result = parsed.get('shouwei_result', {})
        self.shouwei_result.lasu_efficiency = shouwei_result.get('lasu_efficiency', 0.0)
        self.shouwei_result.stability_index = shouwei_result.get('stability_index', 0.0)
        self.shouwei_result.adjusted_weights = shouwei_result.get('adjusted_weights', [])

class ModelStatusResponse:
    def __init__(self):
        self.model_available = False
        self.model_version = ""
        self.last_update = None
        self.prediction_count = 0
        self.average_accuracy = 0.0

class UpdateModelRequest:
    def __init__(self):
        self.device_info = DeviceInfo()
        self.model_data = b""
        self.model_version = ""
        self.force_update = False

class UpdateModelResponse:
    def __init__(self):
        self.success = False
        self.error_message = ""
        self.new_model_version = ""
