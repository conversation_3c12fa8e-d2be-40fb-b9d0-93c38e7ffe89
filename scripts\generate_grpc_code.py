#!/usr/bin/env python3
"""
gRPC代码生成脚本
从proto文件生成Python gRPC代码
"""

import os
import sys
import subprocess
from pathlib import Path

def generate_grpc_code():
    """生成gRPC代码"""
    # 项目根目录
    project_root = Path(__file__).parent.parent
    proto_dir = project_root / "proto"
    output_dir = project_root / "grpc_generated"
    
    # 创建输出目录
    output_dir.mkdir(exist_ok=True)
    
    # 查找所有proto文件
    proto_files = list(proto_dir.glob("*.proto"))
    
    if not proto_files:
        print("未找到proto文件")
        return False
    
    print(f"找到 {len(proto_files)} 个proto文件")
    
    # 生成gRPC代码
    for proto_file in proto_files:
        print(f"生成代码: {proto_file.name}")
        
        cmd = [
            sys.executable, "-m", "grpc_tools.protoc",
            f"--proto_path={proto_dir}",
            f"--python_out={output_dir}",
            f"--grpc_python_out={output_dir}",
            str(proto_file)
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"  ✓ 成功生成: {proto_file.name}")
            else:
                print(f"  ✗ 生成失败: {proto_file.name}")
                print(f"    错误: {result.stderr}")
                return False
        except FileNotFoundError:
            print("错误: grpcio-tools未安装")
            print("请运行: pip install grpcio-tools")
            return False
    
    # 创建__init__.py文件
    init_file = output_dir / "__init__.py"
    init_file.touch()
    
    print(f"gRPC代码生成完成，输出目录: {output_dir}")
    return True

def install_dependencies():
    """安装gRPC依赖"""
    dependencies = [
        "grpcio>=1.50.0",
        "grpcio-tools>=1.50.0",
        "protobuf>=4.21.0"
    ]
    
    print("安装gRPC依赖...")
    for dep in dependencies:
        print(f"安装: {dep}")
        cmd = [sys.executable, "-m", "pip", "install", dep]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"  ✓ 安装成功: {dep}")
        else:
            print(f"  ✗ 安装失败: {dep}")
            print(f"    错误: {result.stderr}")
            return False
    
    return True

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="gRPC代码生成工具")
    parser.add_argument("--install-deps", action="store_true", help="安装gRPC依赖")
    parser.add_argument("--generate", action="store_true", help="生成gRPC代码")
    
    args = parser.parse_args()
    
    if args.install_deps:
        if not install_dependencies():
            sys.exit(1)
    
    if args.generate or not (args.install_deps):
        if not generate_grpc_code():
            sys.exit(1)
    
    print("操作完成")
