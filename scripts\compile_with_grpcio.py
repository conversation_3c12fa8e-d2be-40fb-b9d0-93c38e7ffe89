#!/usr/bin/env python3
"""
使用grpcio-tools编译proto文件
不需要单独安装protoc
"""

import os
import sys
import logging
import shutil
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_grpcio_tools():
    """检查grpcio-tools是否可用"""
    try:
        from grpc_tools import protoc
        logger.info("✓ grpcio-tools 可用")
        return True
    except ImportError:
        logger.error("✗ grpcio-tools 不可用")
        return False

def install_grpcio_tools():
    """安装grpcio-tools"""
    import subprocess
    
    logger.info("安装grpcio-tools...")
    try:
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 'grpcio-tools>=1.50.0'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            logger.info("✓ grpcio-tools 安装成功")
            return True
        else:
            logger.error(f"✗ 安装失败: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"✗ 安装异常: {e}")
        return False

def compile_proto_files():
    """使用grpcio-tools编译proto文件"""
    from grpc_tools import protoc
    import pkg_resources

    project_root = Path(__file__).parent.parent
    proto_dir = project_root / "proto"
    output_dir = project_root / "generated" / "python"

    # 确保输出目录存在
    output_dir.mkdir(parents=True, exist_ok=True)

    # 获取grpc_tools的proto路径（包含google/protobuf/*.proto）
    try:
        grpc_tools_path = pkg_resources.resource_filename('grpc_tools', '_proto')
        logger.info(f"找到grpc_tools proto路径: {grpc_tools_path}")
    except Exception as e:
        logger.warning(f"无法找到grpc_tools proto路径: {e}")
        grpc_tools_path = None
    
    # 查找所有proto文件
    proto_files = list(proto_dir.rglob("*.proto"))
    
    if not proto_files:
        logger.warning("未找到proto文件")
        return False
    
    logger.info(f"找到 {len(proto_files)} 个proto文件")
    
    # 编译每个proto文件
    success_count = 0
    for proto_file in proto_files:
        logger.info(f"编译: {proto_file.name}")
        
        try:
            # 使用绝对路径
            proto_path = str(proto_file)

            # 构建protoc参数 (仅生成Protobuf消息，不生成gRPC)
            protoc_args = [
                'protoc',
                f'--proto_path={proto_dir}',
                f'--python_out={output_dir}'
            ]

            # 如果找到grpc_tools路径，添加到proto_path
            if grpc_tools_path:
                protoc_args.append(f'--proto_path={grpc_tools_path}')

            protoc_args.append(proto_path)

            # 使用grpc_tools.protoc编译
            result = protoc.main(protoc_args)
            
            if result == 0:
                logger.info(f"✓ 编译成功: {proto_file.name}")
                success_count += 1
            else:
                logger.error(f"✗ 编译失败: {proto_file.name}")
        
        except Exception as e:
            logger.error(f"✗ 编译异常 {proto_file.name}: {e}")
    
    logger.info(f"编译完成: {success_count}/{len(proto_files)} 成功")
    return success_count == len(proto_files)

def create_init_files():
    """创建__init__.py文件"""
    project_root = Path(__file__).parent.parent
    generated_dir = project_root / "generated" / "python"
    
    # 需要创建__init__.py的目录
    init_dirs = [
        generated_dir,
        generated_dir / "industrial_control",
        generated_dir / "industrial_control" / "v1"
    ]
    
    for init_dir in init_dirs:
        if init_dir.exists():
            init_file = init_dir / "__init__.py"
            if not init_file.exists():
                init_file.write_text("# Generated protobuf package\n")
                logger.info(f"创建: {init_file}")

def copy_to_dbutil():
    """复制生成的文件到DBUtil目录"""
    project_root = Path(__file__).parent.parent
    source_dir = project_root / "generated" / "python" / "industrial_control"
    target_dir = project_root / "DBUtil" / "industrial_control"
    
    if source_dir.exists():
        if target_dir.exists():
            shutil.rmtree(target_dir)
        shutil.copytree(source_dir, target_dir)
        logger.info(f"复制完成: {source_dir} -> {target_dir}")
        return True
    else:
        logger.warning("生成的文件目录不存在")
        return False

def verify_compilation():
    """验证编译结果"""
    try:
        project_root = Path(__file__).parent.parent
        sys.path.insert(0, str(project_root))
        
        # 导入生成的模块
        from DBUtil.industrial_control.v1 import common_pb2
        from DBUtil.industrial_control.v1 import shouwei_service_pb2
        from DBUtil.industrial_control.v1 import kongwen_service_pb2
        
        # 测试创建消息
        device_info = common_pb2.DeviceInfo()
        device_info.device_id = "test_device"
        device_info.device_type = "shouwei_lasu_control"
        
        # 测试序列化
        serialized = device_info.SerializeToString()
        logger.info(f"✓ 序列化测试成功，大小: {len(serialized)} bytes")
        
        # 测试反序列化
        new_device = common_pb2.DeviceInfo()
        new_device.ParseFromString(serialized)
        
        if new_device.device_id == "test_device":
            logger.info("✓ 反序列化测试成功")
        else:
            logger.error("✗ 反序列化测试失败")
            return False
        
        # 测试首尾控制消息
        shouwei_request = shouwei_service_pb2.ShouweiPredictionRequest()
        shouwei_request.device_info.device_id = "shouwei_001"
        shouwei_request.sensor_data.temperature = 25.5
        shouwei_request.shouwei_data.lasu_coefficient = 1.25
        
        serialized_shouwei = shouwei_request.SerializeToString()
        logger.info(f"✓ 首尾控制消息测试成功，大小: {len(serialized_shouwei)} bytes")
        
        logger.info("✓ 所有验证测试通过")
        return True
        
    except ImportError as e:
        logger.error(f"✗ 导入失败: {e}")
        return False
    except Exception as e:
        logger.error(f"✗ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("使用grpcio-tools编译proto文件")
    print("="*40)
    
    # 1. 检查grpcio-tools
    if not check_grpcio_tools():
        logger.info("尝试安装grpcio-tools...")
        if not install_grpcio_tools():
            print("\n❌ grpcio-tools安装失败")
            print("请手动安装: pip install grpcio-tools")
            return False
    
    # 2. 编译proto文件
    if not compile_proto_files():
        print("\n❌ proto文件编译失败")
        return False
    
    # 3. 创建__init__.py文件
    create_init_files()
    
    # 4. 复制到DBUtil
    if not copy_to_dbutil():
        print("\n❌ 文件复制失败")
        return False
    
    # 5. 验证编译结果
    if not verify_compilation():
        print("\n❌ 编译验证失败")
        return False
    
    print("\n🎉 proto文件编译成功！")
    print("\n✅ 现在您拥有:")
    print("  • 真正的Protobuf二进制序列化")
    print("  • 强类型安全的消息定义")
    print("  • 最大的性能优化")
    
    print("\n🚀 下一步:")
    print("  1. 运行性能测试: python tests/test_protobuf_integration.py")
    print("  2. 测试客户端: python clients/protobuf_client.py")
    print("  3. 启用Protobuf优化: enable_protobuf=True")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
