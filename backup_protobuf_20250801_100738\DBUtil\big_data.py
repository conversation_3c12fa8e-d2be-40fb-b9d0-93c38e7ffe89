from DBUtil.pgsql import PsycopgConn
import pandas as pd
def select_crystalinformation(poolObj,time_start,time_end):
    sql_select_version = 'select * from CrystalInformation where cc_build_time >=\'%s\' and cc_build_time <=\'%s\' ' % (time_start,time_end)
    conn_select_version = poolObj.get_pool_conn()
    #data = PsycopgConn.SelectSql(conn_select_version, sql_select_version)
    data = pd.read_sql(sql_select_version, conn_select_version)
    #result = PsycopgConn.SelectSql(conn_select_version, sql_select_version)
    return data

def select_analoga(poolObj,time_start,time_end,model_name):
    sql_select_version = f"SELECT * FROM {model_name} WHERE cc_time >= '{time_start}' AND cc_time <= '{time_end}'"
    conn_select_version = poolObj.get_pool_conn()
    data = pd.read_sql(sql_select_version, conn_select_version)
    print(data)
    return data


def select_model_statue(poolObj,model_name,model_version):
    sql_select_version = f"select TrainingStatus from DataModel where Name = '{model_name}' and Version = '{model_version}'"
    conn_select_version = poolObj.get_pool_conn()
    result = PsycopgConn.SelectSql(conn_select_version, sql_select_version)
    return result[0][0]

def select_model_version(poolObj,model_name):
    sql_select_version = f"SELECT Version FROM DataModel WHERE Name = '{model_name}' ORDER BY CreateTime DESC LIMIT 1"
    conn_select_version = poolObj.get_pool_conn()
    result = PsycopgConn.SelectSql(conn_select_version, sql_select_version)
    return result[0][0]

def insert_model_train_data(poolObj,model_name,model_version,createtime,IsLatest,TrainingStatus,SampleCount,ResultType):
    sql_insert_version = 'insert into DataModel(Name, Version, CreateTime,IsLatest,TrainingStatus,SampleCount,ResultType) values (\'%s\',\'%s\',\'%s\',%d,%d,%d,%d)' % (model_name, model_version,createtime,IsLatest,TrainingStatus,SampleCount,ResultType)
    conn_select_version = poolObj.get_pool_conn()
    PsycopgConn.InsertSql(conn_select_version, sql_insert_version)


def update_model_data(poolObj,model_name,model_version,samplecount,trainratio,testratio,trainacc,testacc,trainingstatus,modellocation):
    sql_update_version = """
   UPDATE DataModel 
   SET SampleCount = %d,
       TrainSetRatio = %f,
       TestSetRatio = %f,
       TrainSetAccuracy = %f,
       TestSetAccuracy = %f,
       TrainingStatus = %d,
       ModelFileLocation = '%s'
   WHERE Name = '%s' AND Version = '%s'
   """ % (
        int(samplecount),
        float(trainratio),
        float(testratio),
        float(trainacc),
        float(testacc),
        int(trainingstatus),
        str(modellocation),
        str(model_name),
        str(model_version)
    )
    conn_update_version = poolObj.get_pool_conn()
    PsycopgConn.UpdateSql(conn_update_version, sql_update_version)



def insert_std_tiaowentemp(poolObj, x_value,y_result, y_min, y_max, modelID):
    x = 0
    while True:
        if(x < len(x_value)):
            sql = 'insert into ThermostatLiquidModelCurve(ModelID, XAxle, YAxleStand, YAxleMin, YAxleMax) values(%s, %s, %s, %s, %s)' % (modelID[0][0], x_value[x], y_result[x], y_min[x],y_max[x])
            conn = poolObj.get_pool_conn()
            PsycopgConn.InsertSql(conn,sql)
            x = x + 1
        else:
            break
        
def insert_std_tiaowenpower(poolObj, x_value,y_result, y_min, y_max, modelID):
    x = 0
    while True:
        if(x < len(x_value)):
            sql = 'insert into ThermostatPowerModelCurve(ModelID, XAxle, YAxleStand, YAxleMin, YAxleMax) values(%s, %s, %s, %s, %s)' % (modelID[0][0], x_value[x], y_result[x], y_min[x],y_max[x])
            conn = poolObj.get_pool_conn()
            PsycopgConn.InsertSql(conn,sql)
            x = x + 1
        else:
            break

def insert_std_shoulder(poolObj, x_value,y_result, y_min, y_max, modelID):
    for x in range(0,150):
        sql = 'insert into ShoulderModelCurve(ModelID, XAxle, YAxleStand, YAxleMin, YAxleMax) values(%s, %s, %s, %s, %s)' % (modelID[0][0], x_value[x], y_result[x], y_min[x],y_max[x])
        conn = poolObj.get_pool_conn()
        PsycopgConn.InsertSql(conn, sql)

def insert_std_shouldertemp(poolObj, x_value,y_result, y_min, y_max, modelID):
    x = 0
    while True:
        if(x < len(x_value)):
            sql = 'insert into ShoulderLiquidModelCurve(ModelID, XAxle, YAxleStand, YAxleMin, YAxleMax) values(%s, %s, %s, %s, %s)' % (modelID[0][0], x_value[x], y_result[x], y_min[x],y_max[x])
            conn = poolObj.get_pool_conn()
            PsycopgConn.InsertSql(conn, sql)
            x = x + 1
        else:
            break
def insert_std_shoulderpower(poolObj, x_value,y_result, y_min, y_max, modelID):
    x = 0
    while True:
        if(x < len(x_value)):
            sql = 'insert into ShoulderPowerModelCurve(ModelID, XAxle, YAxleStand, YAxleMin, YAxleMax) values(%s, %s, %s, %s, %s)' % (modelID[0][0], x_value[x], y_result[x], y_min[x],y_max[x])
            conn = poolObj.get_pool_conn()
            PsycopgConn.InsertSql(conn, sql)
            x = x + 1
        else:
            break

def insert_std_yekouju(poolObj, x_value,y_result, y_min, y_max, modelID):
    x = 0
    while True:
        if(x < len(x_value)):
            sql = 'insert into LiquidPortModelCurve(ModelID, XAxle, YAxleStand, YAxleMin, YAxleMax) values(%s, %s, %s, %s, %s)' % (modelID[0][0], x_value[x], y_result[x], y_min[x],y_max[x])
            conn = poolObj.get_pool_conn()
            PsycopgConn.InsertSql(conn, sql)
            x = x + 1
        else:
            break


def insert_std_dengjingpower(poolObj, x_value,y_result, y_min, y_max, modelID):
    x = 0
    while True:
        if(x < len(x_value)):
            sql = 'insert into DiameterPowerModelCurve(ModelID, XAxle, YAxleStand, YAxleMin, YAxleMax) values(%s, %s, %s, %s, %s)' % (modelID[0][0], x_value[x], y_result[x], y_min[x],y_max[x])
            conn = poolObj.get_pool_conn()
            PsycopgConn.InsertSql(conn, sql)
            x = x + 1
        else:
            break


def insert_std_yinjingtemp(poolObj, x_value,y_result, y_min, y_max, modelID):
    x = 0
    while True:
        if(x < len(x_value)):
            sql = 'insert into SeedliquidModelCurve(ModelID, XAxle, YAxleStand, YAxleMin, YAxleMax) values(%s, %s, %s, %s, %s)' % (modelID[0][0], x_value[x], y_result[x], y_min[x],y_max[x])
            conn = poolObj.get_pool_conn()
            PsycopgConn.InsertSql(conn, sql)
            x = x + 1
        else:
            break

def insert_std_dengjinglasu(poolObj, data, modelID):
    x = 0
    while True:
        if(x < len(data)):
            sql = 'insert into DiameterSpeedStandModelCurve(ModelID, XAxle, YAxleStand) values(%s, %s, %s)' % (modelID[0][0], x, data[x])
            conn = poolObj.get_pool_conn()
            PsycopgConn.InsertSql(conn, sql)
            x = x + 1
        else:
            break


def insert_dengjing_power_piancha(poolObj, data, modelID):
    x = 0
    while True:
        if(x < len(data)):
            sql = 'insert into DiameterPowerSubStandModelCurve(ModelID, XAxle, YAxleStand) values(%s, %s, %s)' % (modelID[0][0], x, data[x])
            conn = poolObj.get_pool_conn()
            PsycopgConn.InsertSql(conn, sql)
            x = x + 1
        else:
            break

def select_model_id(poolObj,modelName):
    sql_select_version = 'select Id from DataModel where DataModel.`Name`=\'%s\' and DataModel.IsLatest=1' % modelName
    conn_select_version = poolObj.get_pool_conn()
    result = PsycopgConn.SelectSql(conn_select_version, sql_select_version)
    return result

def update_model_version(poolObj, model_name, model_version, sample_count, create_time, funacenums, resulttype, resultvalue, islatest):
    sql_select_version = 'select Version from DataModel where name = \'%s\' and IsLatest = \'%s\'' % (model_name, 1)
    conn_select_version = poolObj.get_pool_conn()
    result = PsycopgConn.SelectSql(conn_select_version, sql_select_version)
    if not result:
        sql_insert_version = 'insert into DataModel(Name, Version, SampleCount, CreateTime, FurnaceNums, ResultType, ResultValue, IsLatest) values (\'%s\',\'%s\',%s,\'%s\',\'%s\',%s,%s,%s)' % (model_name,model_version,sample_count, create_time,funacenums,resulttype,resultvalue,islatest)
        print(sql_insert_version)
        conn_insert_version = poolObj.get_pool_conn()
        PsycopgConn.InsertSql(conn_insert_version,sql_insert_version)
    elif result and result[0][0] != model_version:
        sql_insert_version = 'insert into DataModel(Name, Version, SampleCount, CreateTime, FurnaceNums, ResultType, ResultValue, IsLatest) values (\'%s\',\'%s\',%s,\'%s\',\'%s\',%s,%s,%s)' % (
        model_name, model_version, sample_count, create_time, funacenums, resulttype, resultvalue, islatest)
        print(sql_insert_version)
        conn_insert_version = poolObj.get_pool_conn()
        PsycopgConn.InsertSql(conn_insert_version,sql_insert_version)

        sql_update_version = 'update DataModel set IsLatest = %s where Version = \'%s\'' % (0, result[0][0])
        print(sql_update_version)
        conn_update_version = poolObj.get_pool_conn()
        PsycopgConn.UpdateSql(conn_update_version, sql_update_version)
    else:
        pass
        