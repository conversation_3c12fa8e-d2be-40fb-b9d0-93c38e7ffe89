# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: industrial_control/v1/kongwen_service.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'industrial_control/v1/kongwen_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from industrial_control.v1 import common_pb2 as industrial__control_dot_v1_dot_common__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+industrial_control/v1/kongwen_service.proto\x12\x15industrial_control.v1\x1a\"industrial_control/v1/common.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x92\x02\n\x18KongwenPredictionRequest\x12\x36\n\x0b\x64\x65vice_info\x18\x01 \x01(\x0b\x32!.industrial_control.v1.DeviceInfo\x12\x36\n\x0bsensor_data\x18\x02 \x01(\x0b\x32!.industrial_control.v1.SensorData\x12\x44\n\x12\x63ontrol_parameters\x18\x03 \x01(\x0b\x32(.industrial_control.v1.ControlParameters\x12@\n\x0ckongwen_data\x18\x04 \x01(\x0b\x32*.industrial_control.v1.KongwenSpecificData\"\xab\x01\n\x13KongwenSpecificData\x12\x18\n\x10thermal_capacity\x18\x01 \x01(\x01\x12!\n\x19heat_transfer_coefficient\x18\x02 \x01(\x01\x12\x1b\n\x13\x61mbient_temperature\x18\x03 \x01(\x01\x12:\n\rthermal_model\x18\x04 \x01(\x0b\x32#.industrial_control.v1.ThermalModel\"^\n\x0cThermalModel\x12\x15\n\rtime_constant\x18\x01 \x01(\x01\x12\x0c\n\x04gain\x18\x02 \x01(\x01\x12\x11\n\tdead_time\x18\x03 \x01(\x01\x12\x16\n\x0epid_parameters\x18\x04 \x03(\x01\"\xc6\x01\n\x19KongwenPredictionResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12;\n\nprediction\x18\x03 \x01(\x0b\x32\'.industrial_control.v1.PredictionResult\x12\x44\n\x0ekongwen_result\x18\x04 \x01(\x0b\x32,.industrial_control.v1.KongwenSpecificResult\"\xcb\x01\n\x15KongwenSpecificResult\x12\x1e\n\x16required_heating_power\x18\x01 \x01(\x01\x12\x1e\n\x16required_cooling_power\x18\x02 \x01(\x01\x12\x1d\n\x15temperature_rise_rate\x18\x03 \x01(\x01\x12\x15\n\rsettling_time\x18\x04 \x01(\x01\x12<\n\x0foptimized_model\x18\x05 \x01(\x0b\x32#.industrial_control.v1.ThermalModel\"\xb1\x01\n\x14ThermalModelResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12:\n\rthermal_model\x18\x02 \x01(\x0b\x32#.industrial_control.v1.ThermalModel\x12\x16\n\x0emodel_accuracy\x18\x03 \x01(\x01\x12\x34\n\x10\x63\x61libration_time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xa5\x01\n\x16PIDOptimizationRequest\x12\x36\n\x0b\x64\x65vice_info\x18\x01 \x01(\x0b\x32!.industrial_control.v1.DeviceInfo\x12:\n\rcurrent_model\x18\x02 \x01(\x0b\x32#.industrial_control.v1.ThermalModel\x12\x17\n\x0ftarget_response\x18\x03 \x03(\x01\"x\n\x17PIDOptimizationResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12\x15\n\roptimized_pid\x18\x03 \x03(\x01\x12\x1e\n\x16improvement_percentage\x18\x04 \x01(\x01\x32\xd6\x02\n\x15KongwenControlService\x12l\n\x07Predict\x12/.industrial_control.v1.KongwenPredictionRequest\x1a\x30.industrial_control.v1.KongwenPredictionResponse\x12\x61\n\x0fGetThermalModel\x12!.industrial_control.v1.DeviceInfo\x1a+.industrial_control.v1.ThermalModelResponse\x12l\n\x0bOptimizePID\x12-.industrial_control.v1.PIDOptimizationRequest\x1a..industrial_control.v1.PIDOptimizationResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'industrial_control.v1.kongwen_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_KONGWENPREDICTIONREQUEST']._serialized_start=140
  _globals['_KONGWENPREDICTIONREQUEST']._serialized_end=414
  _globals['_KONGWENSPECIFICDATA']._serialized_start=417
  _globals['_KONGWENSPECIFICDATA']._serialized_end=588
  _globals['_THERMALMODEL']._serialized_start=590
  _globals['_THERMALMODEL']._serialized_end=684
  _globals['_KONGWENPREDICTIONRESPONSE']._serialized_start=687
  _globals['_KONGWENPREDICTIONRESPONSE']._serialized_end=885
  _globals['_KONGWENSPECIFICRESULT']._serialized_start=888
  _globals['_KONGWENSPECIFICRESULT']._serialized_end=1091
  _globals['_THERMALMODELRESPONSE']._serialized_start=1094
  _globals['_THERMALMODELRESPONSE']._serialized_end=1271
  _globals['_PIDOPTIMIZATIONREQUEST']._serialized_start=1274
  _globals['_PIDOPTIMIZATIONREQUEST']._serialized_end=1439
  _globals['_PIDOPTIMIZATIONRESPONSE']._serialized_start=1441
  _globals['_PIDOPTIMIZATIONRESPONSE']._serialized_end=1561
  _globals['_KONGWENCONTROLSERVICE']._serialized_start=1564
  _globals['_KONGWENCONTROLSERVICE']._serialized_end=1906
# @@protoc_insertion_point(module_scope)
