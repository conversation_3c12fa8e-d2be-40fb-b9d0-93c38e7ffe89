# 🚀 Postman测试指南 - Protocol Buffers集成工业控制系统

## 📋 目录
1. [快速开始](#快速开始)
2. [环境配置](#环境配置)
3. [JSON协议测试](#json协议测试)
4. [Protobuf协议测试](#protobuf协议测试)
5. [性能对比测试](#性能对比测试)
6. [错误处理测试](#错误处理测试)
7. [自动化测试](#自动化测试)

## 🚀 快速开始

### 1. 导入Postman集合
1. 打开Postman
2. 点击 `Import` 按钮
3. 选择 `Industrial_Control_API_Tests.postman_collection.json` 文件
4. 导入成功后会看到 "Industrial Control API Tests" 集合

### 2. 设置环境变量
1. 点击右上角的齿轮图标 ⚙️
2. 选择 `Manage Environments`
3. 点击 `Add` 创建新环境
4. 环境名称：`Industrial Control API`
5. 添加变量：
   ```
   base_url: http://localhost:5000
   api_version: v1
   ```

## 🔧 环境配置

### API服务器要求
- **服务器地址**: `http://localhost:5000` (可根据实际情况调整)
- **API版本**: `v1`
- **支持协议**: JSON 和 Protobuf

### 必需的Headers

#### JSON请求
```
Content-Type: application/json
Accept: application/json
```

#### Protobuf请求
```
Content-Type: application/x-protobuf
Accept: application/x-protobuf
```

## 📝 JSON协议测试

### 1. 首尾控制预测测试

**端点**: `POST {{base_url}}/api/v1/shouwei/predict`

**完整请求示例**:
```json
{
  "device_info": {
    "device_id": "shouwei_device_001",
    "device_type": "shouwei_lasu_control",
    "location": "production_line_1"
  },
  "sensor_data": {
    "temperature": 25.5,
    "pressure": 1.2,
    "flow_rate": 15.8,
    "power_consumption": 850.0,
    "humidity": 65.2,
    "vibration": 0.05,
    "additional_sensors": {
      "oil_temperature": 45.2,
      "bearing_temperature": 38.5
    }
  },
  "control_parameters": {
    "target_temperature": 26.0,
    "max_power_limit": 1000,
    "control_precision": 0.1,
    "emergency_stop": false,
    "additional_params": {
      "pid_kp": 1.2,
      "pid_ki": 0.8,
      "pid_kd": 0.3
    }
  },
  "shouwei_data": {
    "lasu_coefficient": 1.25,
    "baseline_power": 500.0,
    "weight_corrections": [0.95, 1.02, 0.98, 1.01, 0.99],
    "power_distribution": {
      "main_ratio": 0.7,
      "vice_ratio": 0.25,
      "reserve_ratio": 0.05
    }
  },
  "timestamp": 1691234567890,
  "request_id": "req_shouwei_001"
}
```

**预期响应**:
```json
{
  "success": true,
  "prediction": {
    "main_power": 720.5,
    "vice_power": 180.2,
    "total_power": 900.7,
    "predicted_temperature": 26.1,
    "confidence": 0.95,
    "efficiency": 0.87
  },
  "shouwei_result": {
    "lasu_efficiency": 0.92,
    "stability_index": 0.88,
    "adjusted_weights": [0.96, 1.01, 0.99, 1.00, 0.98]
  },
  "metadata": {
    "protocol": "json",
    "processing_time_ms": 45.2,
    "model_version": "v1.0",
    "cache_hit": false
  }
}
```

### 2. 控温预测测试

**端点**: `POST {{base_url}}/api/v1/kongwen/predict`

**完整请求示例**:
```json
{
  "device_info": {
    "device_id": "kongwen_device_001",
    "device_type": "kongwen_power_control",
    "location": "thermal_control_zone_A"
  },
  "sensor_data": {
    "temperature": 24.8,
    "pressure": 1.1,
    "flow_rate": 12.5,
    "power_consumption": 750.0,
    "humidity": 58.3,
    "vibration": 0.03,
    "additional_sensors": {
      "ambient_temperature": 20.0,
      "surface_temperature": 28.5
    }
  },
  "control_parameters": {
    "target_temperature": 25.5,
    "max_power_limit": 800,
    "control_precision": 0.05,
    "emergency_stop": false,
    "additional_params": {
      "heating_mode": "gradual",
      "cooling_mode": "active"
    }
  },
  "kongwen_data": {
    "thermal_capacity": 1000.0,
    "heat_transfer_coefficient": 10.0,
    "ambient_temperature": 20.0,
    "thermal_model": {
      "time_constant": 120.0,
      "gain": 0.85,
      "dead_time": 5.0,
      "pid_parameters": [1.5, 0.6, 0.2]
    }
  },
  "timestamp": 1691234567890,
  "request_id": "req_kongwen_001"
}
```

## 🔧 Protobuf协议测试

### 1. 准备Protobuf二进制数据

使用提供的二进制文件：
- `shouwei_request.bin` - 首尾控制请求
- `kongwen_request.bin` - 控温请求

### 2. 在Postman中发送Protobuf请求

1. **选择请求方法**: `POST`
2. **设置URL**: `{{base_url}}/api/v1/shouwei/predict`
3. **设置Headers**:
   ```
   Content-Type: application/x-protobuf
   Accept: application/x-protobuf
   ```
4. **设置Body**:
   - 选择 `binary`
   - 点击 `Select File`
   - 选择 `shouwei_request.bin` 文件
5. **发送请求**

### 3. Protobuf响应验证

Protobuf响应是二进制格式，在Postman中会显示为二进制数据。要查看内容，可以：

1. **使用十六进制查看器**查看原始数据
2. **检查响应大小** - 应该比JSON小60%左右
3. **验证Content-Type** - 应该是 `application/x-protobuf`

## 📊 性能对比测试

### 1. 响应时间对比

在Postman中查看 `Time` 字段：

| 协议 | 预期响应时间 | 数据大小 |
|------|-------------|----------|
| JSON | 50-100ms | ~520 bytes |
| Protobuf | 20-50ms | ~210 bytes |

### 2. 数据大小对比

在Postman的响应区域查看：
- **JSON**: 查看 `Size` 字段
- **Protobuf**: 查看 `Size` 字段，应该显著小于JSON

### 3. 批量性能测试

使用Postman的 `Collection Runner` 功能：

1. 选择 "Industrial Control API Tests" 集合
2. 设置迭代次数：100
3. 设置延迟：100ms
4. 运行测试
5. 查看平均响应时间和成功率

## ❌ 错误处理测试

### 1. 无效数据测试

**测试用例**: 发送无效的温度值
```json
{
  "device_info": {
    "device_id": "test_device"
  },
  "sensor_data": {
    "temperature": -999.9  // 无效温度
  }
}
```

**预期响应**:
```json
{
  "success": false,
  "error_message": "Invalid temperature value",
  "error_code": 400
}
```

### 2. 缺失字段测试

**测试用例**: 缺少必需字段
```json
{
  "device_info": {
    // 缺少 device_id
    "device_type": "shouwei_lasu_control"
  }
}
```

### 3. 协议不匹配测试

**测试用例**: 发送JSON数据但设置Protobuf Content-Type
```
Content-Type: application/x-protobuf
Body: JSON数据
```

## 🤖 自动化测试

### 1. 测试脚本

在Postman的 `Tests` 标签中添加：

```javascript
// 验证响应状态
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

// 验证响应时间
pm.test("Response time is less than 200ms", function () {
    pm.expect(pm.response.responseTime).to.be.below(200);
});

// 验证JSON响应结构
if (pm.response.headers.get("Content-Type").includes("application/json")) {
    pm.test("Response has prediction data", function () {
        var jsonData = pm.response.json();
        pm.expect(jsonData).to.have.property('success');
        pm.expect(jsonData).to.have.property('prediction');
    });
}

// 验证Protobuf响应
if (pm.response.headers.get("Content-Type").includes("application/x-protobuf")) {
    pm.test("Protobuf response size is smaller", function () {
        pm.expect(pm.response.responseSize).to.be.below(300);
    });
}
```

### 2. 环境变量设置

```javascript
// 在Pre-request Script中设置动态数据
pm.environment.set("timestamp", Date.now());
pm.environment.set("request_id", "req_" + Math.random().toString(36).substr(2, 9));
```

## 🎯 测试检查清单

### ✅ 基础功能测试
- [ ] 健康检查端点响应正常
- [ ] 首尾控制JSON请求成功
- [ ] 控温JSON请求成功
- [ ] 向后兼容的通用预测端点工作正常

### ✅ Protobuf功能测试
- [ ] 首尾控制Protobuf请求成功
- [ ] 控温Protobuf请求成功
- [ ] Protobuf响应大小明显小于JSON
- [ ] Protobuf响应时间优于JSON

### ✅ 错误处理测试
- [ ] 无效数据返回适当错误
- [ ] 缺失字段返回验证错误
- [ ] 协议不匹配返回错误
- [ ] 服务器错误返回500状态码

### ✅ 性能测试
- [ ] JSON响应时间 < 100ms
- [ ] Protobuf响应时间 < 50ms
- [ ] Protobuf数据大小减少 > 50%
- [ ] 批量测试成功率 > 99%

## 🔧 故障排除

### 常见问题

1. **连接失败**
   - 检查服务器是否运行在正确端口
   - 验证base_url环境变量设置

2. **Protobuf请求失败**
   - 确认Content-Type设置为 `application/x-protobuf`
   - 检查二进制文件是否正确上传

3. **响应解析错误**
   - 检查Accept header设置
   - 验证服务器支持请求的协议

4. **性能不如预期**
   - 检查网络延迟
   - 验证服务器负载
   - 确认使用了正确的序列化方法

---

🎉 **恭喜！您现在可以全面测试Protocol Buffers集成的工业控制系统了！**
