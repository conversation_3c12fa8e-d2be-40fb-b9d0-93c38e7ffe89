# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# 这是一个模拟的protobuf模块，用于演示集成
# 实际使用时需要通过protoc编译生成

class DeviceInfo:
    def __init__(self):
        self.device_id = ""
        self.device_type = ""
        self.location = ""
        self.last_update = None

class SensorData:
    def __init__(self):
        self.temperature = 0.0
        self.pressure = 0.0
        self.flow_rate = 0.0
        self.power_consumption = 0.0
        self.humidity = 0.0
        self.vibration = 0.0
        self.additional_sensors = {}

class ControlParameters:
    def __init__(self):
        self.target_temperature = 0.0
        self.max_power_limit = 0
        self.control_precision = 0.0
        self.emergency_stop = False
        self.additional_params = {}

class PredictionResult:
    def __init__(self):
        self.main_power = 0.0
        self.vice_power = 0.0
        self.total_power = 0.0
        self.predicted_temperature = 0.0
        self.confidence = 0.0
        self.efficiency = 0.0
        self.control_strategy = None
        self.metadata = PredictionMetadata()

class ControlStrategy:
    def __init__(self):
        self.main_power_setpoint = 0.0
        self.vice_power_setpoint = 0.0
        self.control_mode = 0
        self.actions = []

class ControlAction:
    def __init__(self):
        self.action_type = ""
        self.value = 0.0
        self.execute_time = None
        self.priority = 0

class PredictionMetadata:
    def __init__(self):
        self.model_version = ""
        self.prediction_time = None
        self.processing_time_ms = 0
        self.cache_hit = False
        self.serialization_method = ""

class CommonResponse:
    def __init__(self):
        self.success = False
        self.error_message = ""
        self.error_code = 0
    
    def SerializeToString(self):
        """模拟序列化方法"""
        import json
        data = {
            'success': self.success,
            'error_message': self.error_message,
            'error_code': self.error_code
        }
        return json.dumps(data).encode('utf-8')
    
    def ParseFromString(self, data):
        """模拟反序列化方法"""
        import json
        parsed = json.loads(data.decode('utf-8'))
        self.success = parsed.get('success', False)
        self.error_message = parsed.get('error_message', '')
        self.error_code = parsed.get('error_code', 0)
