#!/usr/bin/env python3
"""
模型传输性能测试
对比 Pickle vs MessagePack+LZ4 的传输速度和压缩效果
"""

import sys
import os
import time
import json
import pickle
import tempfile
import unittest
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from DBUtil.serialization_manager import SerializationManager, SerializationMethod

@dataclass
class PerformanceResult:
    """性能测试结果"""
    method: str
    data_size_mb: float
    serialize_time_ms: float
    deserialize_time_ms: float
    total_time_ms: float
    serialized_size_bytes: int
    compression_ratio: float
    throughput_mbps: float

class ModelDataGenerator:
    """模型数据生成器"""
    
    @staticmethod
    def generate_neural_network_model(size_mb: float) -> Dict[str, Any]:
        """生成神经网络模型数据"""
        import numpy as np
        
        # 计算需要的参数数量来达到目标大小
        target_bytes = size_mb * 1024 * 1024
        # 假设每个float64参数占8字节
        num_params = int(target_bytes / 8 / 4)  # 分配给4层
        
        return {
            'model_type': 'neural_network',
            'version': '1.0',
            'architecture': {
                'input_layer': {
                    'weights': np.random.randn(num_params, 128).tolist(),
                    'biases': np.random.randn(128).tolist()
                },
                'hidden_layer_1': {
                    'weights': np.random.randn(128, 256).tolist(),
                    'biases': np.random.randn(256).tolist()
                },
                'hidden_layer_2': {
                    'weights': np.random.randn(256, 128).tolist(),
                    'biases': np.random.randn(128).tolist()
                },
                'output_layer': {
                    'weights': np.random.randn(128, 1).tolist(),
                    'biases': np.random.randn(1).tolist()
                }
            },
            'training_metadata': {
                'epochs': 100,
                'learning_rate': 0.001,
                'batch_size': 32,
                'accuracy': 0.95,
                'loss': 0.05,
                'optimizer': 'adam',
                'training_time_hours': 2.5
            },
            'feature_names': [f'feature_{i}' for i in range(min(1000, num_params // 100))],
            'hyperparameters': {
                'dropout_rate': 0.2,
                'regularization_l1': 0.01,
                'regularization_l2': 0.01,
                'activation_function': 'relu',
                'output_activation': 'sigmoid'
            }
        }
    
    @staticmethod
    def generate_industrial_control_model(size_mb: float) -> Dict[str, Any]:
        """生成工业控制模型数据（模拟shouwei/kongwen模型）"""
        import numpy as np
        
        target_bytes = size_mb * 1024 * 1024
        num_control_points = int(target_bytes / 8 / 10)  # 分配给控制点数据
        
        return {
            'model_type': 'industrial_control',
            'control_type': 'shouwei_lasu',
            'device_id': 'device_001',
            'version': 'v1.0',
            'control_parameters': {
                'power_baseline': np.random.uniform(50, 150, num_control_points).tolist(),
                'lasu_baseline': np.random.uniform(1.0, 5.0, num_control_points).tolist(),
                'weight_corrections': {
                    f'weight_{i}': np.random.uniform(-0.5, 0.5, 100).tolist() 
                    for i in range(min(100, num_control_points // 100))
                }
            },
            'calibration_data': {
                'height_weight_mapping': {
                    str(h): np.random.uniform(0.8, 1.2, 50).tolist()
                    for h in range(100, 200, 5)
                },
                'temperature_curves': np.random.uniform(1000, 1500, num_control_points // 2).tolist(),
                'pressure_profiles': np.random.uniform(0.1, 2.0, num_control_points // 2).tolist()
            },
            'historical_data': {
                'production_records': [
                    {
                        'timestamp': time.time() - i * 3600,
                        'height': np.random.uniform(100, 200),
                        'weight': np.random.uniform(50, 150),
                        'power': np.random.uniform(80, 120),
                        'lasu': np.random.uniform(2.0, 4.0),
                        'quality_score': np.random.uniform(0.8, 1.0)
                    }
                    for i in range(min(1000, num_control_points // 10))
                ]
            },
            'model_metadata': {
                'last_trained': time.time(),
                'training_samples': num_control_points,
                'validation_accuracy': 0.92,
                'model_size_mb': size_mb,
                'feature_importance': {
                    'height': 0.35,
                    'weight': 0.30,
                    'temperature': 0.20,
                    'pressure': 0.15
                }
            }
        }

class ModelTransmissionPerformanceTest(unittest.TestCase):
    """模型传输性能测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.serializer = SerializationManager()
        self.test_sizes = [1, 10, 50]  # MB
        self.results: List[PerformanceResult] = []
        
    def test_neural_network_models(self):
        """测试神经网络模型的传输性能"""
        print("\n" + "="*80)
        print("神经网络模型传输性能测试")
        print("="*80)
        
        for size_mb in self.test_sizes:
            print(f"\n测试模型大小: {size_mb} MB")
            print("-" * 60)
            
            # 生成测试数据
            model_data = ModelDataGenerator.generate_neural_network_model(size_mb)
            
            # 测试不同序列化方法
            methods = [
                (SerializationMethod.PICKLE, "Pickle"),
                (SerializationMethod.MSGPACK, "MessagePack"),
                (SerializationMethod.MSGPACK_LZ4, "MessagePack+LZ4")
            ]
            
            for method, method_name in methods:
                try:
                    result = self._benchmark_serialization(model_data, method, method_name, size_mb)
                    self.results.append(result)
                    self._print_result(result)
                except Exception as e:
                    print(f"  {method_name}: 测试失败 - {e}")
    
    def test_industrial_control_models(self):
        """测试工业控制模型的传输性能"""
        print("\n" + "="*80)
        print("工业控制模型传输性能测试")
        print("="*80)
        
        for size_mb in self.test_sizes:
            print(f"\n测试模型大小: {size_mb} MB")
            print("-" * 60)
            
            # 生成测试数据
            model_data = ModelDataGenerator.generate_industrial_control_model(size_mb)
            
            # 测试不同序列化方法
            methods = [
                (SerializationMethod.PICKLE, "Pickle"),
                (SerializationMethod.MSGPACK_LZ4, "MessagePack+LZ4")
            ]
            
            for method, method_name in methods:
                try:
                    result = self._benchmark_serialization(model_data, method, method_name, size_mb)
                    self.results.append(result)
                    self._print_result(result)
                except Exception as e:
                    print(f"  {method_name}: 测试失败 - {e}")
    
    def test_redis_simulation(self):
        """模拟Redis存储和读取性能"""
        print("\n" + "="*80)
        print("Redis存储模拟性能测试")
        print("="*80)
        
        # 使用中等大小的模型进行Redis模拟测试
        model_data = ModelDataGenerator.generate_industrial_control_model(10)
        
        # 模拟Redis存储（使用临时文件）
        with tempfile.TemporaryDirectory() as temp_dir:
            for method, method_name in [(SerializationMethod.PICKLE, "Pickle"), 
                                      (SerializationMethod.MSGPACK_LZ4, "MessagePack+LZ4")]:
                try:
                    result = self._benchmark_redis_simulation(model_data, method, method_name, temp_dir)
                    print(f"\n{method_name} Redis模拟:")
                    print(f"  存储时间: {result['store_time_ms']:.2f} ms")
                    print(f"  读取时间: {result['load_time_ms']:.2f} ms")
                    print(f"  总时间: {result['total_time_ms']:.2f} ms")
                    print(f"  文件大小: {result['file_size_mb']:.2f} MB")
                except Exception as e:
                    print(f"  {method_name} Redis模拟失败: {e}")
    
    def _benchmark_serialization(self, data: Any, method: SerializationMethod, 
                                method_name: str, expected_size_mb: float) -> PerformanceResult:
        """基准测试序列化性能"""
        # 序列化测试
        start_time = time.time()
        serialized_data = self.serializer.serialize(data, method)
        serialize_time = (time.time() - start_time) * 1000
        
        # 反序列化测试
        start_time = time.time()
        deserialized_data = self.serializer.deserialize(serialized_data, method)
        deserialize_time = (time.time() - start_time) * 1000
        
        # 验证数据完整性
        self.assertEqual(deserialized_data['model_type'], data['model_type'])
        
        # 计算性能指标
        serialized_size = len(serialized_data)
        total_time = serialize_time + deserialize_time
        
        # 计算压缩比（相对于原始数据的估算大小）
        estimated_original_size = len(pickle.dumps(data))
        compression_ratio = serialized_size / estimated_original_size
        
        # 计算吞吐量 (MB/s)
        throughput = (serialized_size / (1024 * 1024)) / (total_time / 1000)
        
        return PerformanceResult(
            method=method_name,
            data_size_mb=expected_size_mb,
            serialize_time_ms=serialize_time,
            deserialize_time_ms=deserialize_time,
            total_time_ms=total_time,
            serialized_size_bytes=serialized_size,
            compression_ratio=compression_ratio,
            throughput_mbps=throughput
        )
    
    def _benchmark_redis_simulation(self, data: Any, method: SerializationMethod, 
                                  method_name: str, temp_dir: str) -> Dict[str, float]:
        """模拟Redis存储性能测试"""
        file_path = Path(temp_dir) / f"redis_sim_{method_name.lower()}.dat"
        
        # 序列化数据
        serialized_data = self.serializer.serialize(data, method)
        
        # 模拟存储到Redis（写入文件）
        start_time = time.time()
        with open(file_path, 'wb') as f:
            f.write(serialized_data)
        store_time = (time.time() - start_time) * 1000
        
        # 模拟从Redis读取（读取文件）
        start_time = time.time()
        with open(file_path, 'rb') as f:
            loaded_data = f.read()
        load_time = (time.time() - start_time) * 1000
        
        # 反序列化
        start_time = time.time()
        self.serializer.deserialize(loaded_data, method)
        deserialize_time = (time.time() - start_time) * 1000
        
        file_size_mb = file_path.stat().st_size / (1024 * 1024)
        
        return {
            'store_time_ms': store_time,
            'load_time_ms': load_time + deserialize_time,
            'total_time_ms': store_time + load_time + deserialize_time,
            'file_size_mb': file_size_mb
        }
    
    def _print_result(self, result: PerformanceResult):
        """打印测试结果"""
        print(f"  {result.method}:")
        print(f"    序列化时间: {result.serialize_time_ms:.2f} ms")
        print(f"    反序列化时间: {result.deserialize_time_ms:.2f} ms")
        print(f"    总时间: {result.total_time_ms:.2f} ms")
        print(f"    数据大小: {result.serialized_size_bytes / (1024*1024):.2f} MB")
        print(f"    压缩比: {result.compression_ratio:.3f}")
        print(f"    吞吐量: {result.throughput_mbps:.2f} MB/s")
    
    def tearDown(self):
        """测试后清理"""
        if self.results:
            self._generate_performance_report()
    
    def _generate_performance_report(self):
        """生成性能报告"""
        print("\n" + "="*80)
        print("性能测试总结报告")
        print("="*80)
        
        # 按方法分组结果
        method_results = {}
        for result in self.results:
            if result.method not in method_results:
                method_results[result.method] = []
            method_results[result.method].append(result)
        
        # 计算平均性能
        print("\n平均性能对比:")
        print("-" * 60)
        for method, results in method_results.items():
            avg_serialize_time = sum(r.serialize_time_ms for r in results) / len(results)
            avg_deserialize_time = sum(r.deserialize_time_ms for r in results) / len(results)
            avg_total_time = sum(r.total_time_ms for r in results) / len(results)
            avg_compression_ratio = sum(r.compression_ratio for r in results) / len(results)
            avg_throughput = sum(r.throughput_mbps for r in results) / len(results)
            
            print(f"{method}:")
            print(f"  平均序列化时间: {avg_serialize_time:.2f} ms")
            print(f"  平均反序列化时间: {avg_deserialize_time:.2f} ms")
            print(f"  平均总时间: {avg_total_time:.2f} ms")
            print(f"  平均压缩比: {avg_compression_ratio:.3f}")
            print(f"  平均吞吐量: {avg_throughput:.2f} MB/s")
            print()
        
        # 保存详细报告到文件
        self._save_detailed_report()
    
    def _save_detailed_report(self):
        """保存详细报告到文件"""
        report_data = {
            'test_timestamp': time.time(),
            'test_summary': {
                'total_tests': len(self.results),
                'test_sizes_mb': self.test_sizes
            },
            'detailed_results': [
                {
                    'method': r.method,
                    'data_size_mb': r.data_size_mb,
                    'serialize_time_ms': r.serialize_time_ms,
                    'deserialize_time_ms': r.deserialize_time_ms,
                    'total_time_ms': r.total_time_ms,
                    'serialized_size_bytes': r.serialized_size_bytes,
                    'compression_ratio': r.compression_ratio,
                    'throughput_mbps': r.throughput_mbps
                }
                for r in self.results
            ]
        }
        
        report_file = Path(__file__).parent / f"performance_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"详细报告已保存到: {report_file}")

if __name__ == '__main__':
    # 运行性能测试
    unittest.main(verbosity=2)
