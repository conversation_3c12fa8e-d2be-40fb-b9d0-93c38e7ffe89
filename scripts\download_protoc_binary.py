#!/usr/bin/env python3
"""
下载预编译的protoc二进制文件
"""

import os
import sys
import urllib.request
import zipfile
import shutil
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def download_protoc_binary():
    """下载预编译的protoc二进制文件"""
    
    # 使用较新的稳定版本
    version = "25.1"
    filename = f"protoc-{version}-win64.zip"
    url = f"https://github.com/protocolbuffers/protobuf/releases/download/v{version}/{filename}"
    
    # 下载到您现有的apps目录
    download_dir = Path("D:/apps/protoc-binary")
    download_dir.mkdir(parents=True, exist_ok=True)
    
    zip_path = download_dir / filename
    
    logger.info(f"下载URL: {url}")
    logger.info(f"下载到: {zip_path}")
    
    try:
        logger.info("开始下载...")
        urllib.request.urlretrieve(url, zip_path)
        logger.info("下载完成")
        
        # 解压
        logger.info("解压文件...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(download_dir)
        
        # 删除zip文件
        zip_path.unlink()
        
        # 检查protoc.exe
        protoc_exe = download_dir / "bin" / "protoc.exe"
        if protoc_exe.exists():
            logger.info(f"✓ protoc.exe 已准备就绪: {protoc_exe}")
            return str(protoc_exe)
        else:
            logger.error("protoc.exe 未找到")
            return None
            
    except Exception as e:
        logger.error(f"下载失败: {e}")
        return None

def main():
    """主函数"""
    print("下载预编译protoc二进制文件")
    print("="*40)
    
    protoc_path = download_protoc_binary()
    
    if protoc_path:
        print(f"\n🎉 protoc下载成功！")
        print(f"位置: {protoc_path}")
        print(f"\n现在可以使用以下命令编译proto文件:")
        print(f'"{protoc_path}" --version')
    else:
        print("\n❌ 下载失败")
        print("请手动下载:")
        print("1. 访问: https://github.com/protocolbuffers/protobuf/releases")
        print("2. 下载: protoc-25.1-win64.zip")
        print("3. 解压到: D:\\apps\\protoc-binary\\")
    
    return protoc_path is not None

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
