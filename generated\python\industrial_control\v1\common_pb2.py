# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: industrial_control/v1/common.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'industrial_control/v1/common.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\"industrial_control/v1/common.proto\x12\x15industrial_control.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"w\n\nDeviceInfo\x12\x11\n\tdevice_id\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65vice_type\x18\x02 \x01(\t\x12\x10\n\x08location\x18\x03 \x01(\t\x12/\n\x0blast_update\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\x96\x02\n\nSensorData\x12\x13\n\x0btemperature\x18\x01 \x01(\x01\x12\x10\n\x08pressure\x18\x02 \x01(\x01\x12\x11\n\tflow_rate\x18\x03 \x01(\x01\x12\x19\n\x11power_consumption\x18\x04 \x01(\x01\x12\x10\n\x08humidity\x18\x05 \x01(\x01\x12\x11\n\tvibration\x18\x06 \x01(\x01\x12T\n\x12\x61\x64\x64itional_sensors\x18\n \x03(\x0b\x32\x38.industrial_control.v1.SensorData.AdditionalSensorsEntry\x1a\x38\n\x16\x41\x64\x64itionalSensorsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01:\x02\x38\x01\"\x8f\x02\n\x11\x43ontrolParameters\x12\x1a\n\x12target_temperature\x18\x01 \x01(\x01\x12\x17\n\x0fmax_power_limit\x18\x02 \x01(\x05\x12\x19\n\x11\x63ontrol_precision\x18\x03 \x01(\x01\x12\x16\n\x0e\x65mergency_stop\x18\x04 \x01(\x08\x12Y\n\x11\x61\x64\x64itional_params\x18\n \x03(\x0b\x32>.industrial_control.v1.ControlParameters.AdditionalParamsEntry\x1a\x37\n\x15\x41\x64\x64itionalParamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01:\x02\x38\x01\"\x95\x02\n\x10PredictionResult\x12\x12\n\nmain_power\x18\x01 \x01(\x01\x12\x12\n\nvice_power\x18\x02 \x01(\x01\x12\x13\n\x0btotal_power\x18\x03 \x01(\x01\x12\x1d\n\x15predicted_temperature\x18\x04 \x01(\x01\x12\x12\n\nconfidence\x18\x05 \x01(\x01\x12\x12\n\nefficiency\x18\x06 \x01(\x01\x12@\n\x10\x63ontrol_strategy\x18\x07 \x01(\x0b\x32&.industrial_control.v1.ControlStrategy\x12;\n\x08metadata\x18\x08 \x01(\x0b\x32).industrial_control.v1.PredictionMetadata\"\x98\x01\n\x0f\x43ontrolStrategy\x12\x1b\n\x13main_power_setpoint\x18\x01 \x01(\x01\x12\x1b\n\x13vice_power_setpoint\x18\x02 \x01(\x01\x12\x14\n\x0c\x63ontrol_mode\x18\x03 \x01(\x05\x12\x35\n\x07\x61\x63tions\x18\x04 \x03(\x0b\x32$.industrial_control.v1.ControlAction\"w\n\rControlAction\x12\x13\n\x0b\x61\x63tion_type\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01\x12\x30\n\x0c\x65xecute_time\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x10\n\x08priority\x18\x04 \x01(\x05\"\xad\x01\n\x12PredictionMetadata\x12\x15\n\rmodel_version\x18\x01 \x01(\t\x12\x33\n\x0fprediction_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1a\n\x12processing_time_ms\x18\x03 \x01(\x05\x12\x11\n\tcache_hit\x18\x04 \x01(\x08\x12\x1c\n\x14serialization_method\x18\x05 \x01(\t\"L\n\x0e\x43ommonResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12\x12\n\nerror_code\x18\x03 \x01(\x05\x42s\n!com.company.industrial.control.v1Z.github.com/company/industrial-control/proto/v1\xaa\x02\x1d\x43ompany.Industrial.Control.V1b\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'industrial_control.v1.common_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n!com.company.industrial.control.v1Z.github.com/company/industrial-control/proto/v1\252\002\035Company.Industrial.Control.V1'
  _globals['_SENSORDATA_ADDITIONALSENSORSENTRY']._loaded_options = None
  _globals['_SENSORDATA_ADDITIONALSENSORSENTRY']._serialized_options = b'8\001'
  _globals['_CONTROLPARAMETERS_ADDITIONALPARAMSENTRY']._loaded_options = None
  _globals['_CONTROLPARAMETERS_ADDITIONALPARAMSENTRY']._serialized_options = b'8\001'
  _globals['_DEVICEINFO']._serialized_start=94
  _globals['_DEVICEINFO']._serialized_end=213
  _globals['_SENSORDATA']._serialized_start=216
  _globals['_SENSORDATA']._serialized_end=494
  _globals['_SENSORDATA_ADDITIONALSENSORSENTRY']._serialized_start=438
  _globals['_SENSORDATA_ADDITIONALSENSORSENTRY']._serialized_end=494
  _globals['_CONTROLPARAMETERS']._serialized_start=497
  _globals['_CONTROLPARAMETERS']._serialized_end=768
  _globals['_CONTROLPARAMETERS_ADDITIONALPARAMSENTRY']._serialized_start=713
  _globals['_CONTROLPARAMETERS_ADDITIONALPARAMSENTRY']._serialized_end=768
  _globals['_PREDICTIONRESULT']._serialized_start=771
  _globals['_PREDICTIONRESULT']._serialized_end=1048
  _globals['_CONTROLSTRATEGY']._serialized_start=1051
  _globals['_CONTROLSTRATEGY']._serialized_end=1203
  _globals['_CONTROLACTION']._serialized_start=1205
  _globals['_CONTROLACTION']._serialized_end=1324
  _globals['_PREDICTIONMETADATA']._serialized_start=1327
  _globals['_PREDICTIONMETADATA']._serialized_end=1500
  _globals['_COMMONRESPONSE']._serialized_start=1502
  _globals['_COMMONRESPONSE']._serialized_end=1578
# @@protoc_insertion_point(module_scope)
