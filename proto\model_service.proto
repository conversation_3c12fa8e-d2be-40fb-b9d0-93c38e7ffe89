syntax = "proto3";

package bigdata.model;

// 模型服务定义
service ModelService {
    // 模型预测
    rpc Predict(PredictRequest) returns (PredictResponse);
    
    // 批量预测
    rpc BatchPredict(BatchPredictRequest) returns (BatchPredictResponse);
    
    // 流式预测
    rpc StreamPredict(stream PredictRequest) returns (stream PredictResponse);
    
    // 模型设置
    rpc SetupModel(SetupModelRequest) returns (SetupModelResponse);
    
    // 获取模型信息
    rpc GetModelInfo(GetModelInfoRequest) returns (GetModelInfoResponse);
    
    // 健康检查
    rpc HealthCheck(HealthCheckRequest) returns (HealthCheckResponse);
    
    // 获取性能统计
    rpc GetStats(GetStatsRequest) returns (GetStatsResponse);
}

// 预测请求
message PredictRequest {
    string model_name = 1;
    string device_id = 2;
    string version = 3;
    map<string, double> features = 4;
    int64 timestamp = 5;
    string request_id = 6;
}

// 预测响应
message PredictResponse {
    bool success = 1;
    map<string, double> predictions = 2;
    string error_message = 3;
    double processing_time_ms = 4;
    string model_version = 5;
    string request_id = 6;
    int64 timestamp = 7;
}

// 批量预测请求
message BatchPredictRequest {
    string model_name = 1;
    string device_id = 2;
    string version = 3;
    repeated map<string, double> batch_features = 4;
    int64 timestamp = 5;
    string request_id = 6;
}

// 批量预测响应
message BatchPredictResponse {
    bool success = 1;
    repeated map<string, double> batch_predictions = 2;
    string error_message = 3;
    double processing_time_ms = 4;
    string model_version = 5;
    string request_id = 6;
    int64 timestamp = 7;
}

// 模型设置请求
message SetupModelRequest {
    string model_name = 1;
    string device_id = 2;
    string version = 3;
    ModelConfig config = 4;
    map<string, double> parameters = 5;
    int64 timestamp = 6;
}

// 模型配置
message ModelConfig {
    string product_type = 1;
    string field_size = 2;
    double power = 3;
    double diameter = 4;
    double weight = 5;
    repeated double target_dia = 6;
    repeated double target_power = 7;
    map<string, string> additional_config = 8;
}

// 模型设置响应
message SetupModelResponse {
    bool success = 1;
    string error_message = 2;
    map<string, double> baseline_values = 3;
    double processing_time_ms = 4;
    int64 timestamp = 5;
}

// 获取模型信息请求
message GetModelInfoRequest {
    string model_name = 1;
    string device_id = 2;
    string version = 3;
}

// 获取模型信息响应
message GetModelInfoResponse {
    bool success = 1;
    string error_message = 2;
    ModelInfo model_info = 3;
}

// 模型信息
message ModelInfo {
    string model_name = 1;
    string device_id = 2;
    string version = 3;
    string model_type = 4;
    int64 last_updated = 5;
    int64 last_accessed = 6;
    int32 access_count = 7;
    double model_size_mb = 8;
    map<string, string> metadata = 9;
}

// 健康检查请求
message HealthCheckRequest {
    string service = 1;
}

// 健康检查响应
message HealthCheckResponse {
    enum ServingStatus {
        UNKNOWN = 0;
        SERVING = 1;
        NOT_SERVING = 2;
        SERVICE_UNKNOWN = 3;
    }
    ServingStatus status = 1;
    string message = 2;
    int64 timestamp = 3;
}

// 获取统计请求
message GetStatsRequest {
    bool include_detailed = 1;
    int64 time_range_start = 2;
    int64 time_range_end = 3;
}

// 获取统计响应
message GetStatsResponse {
    bool success = 1;
    string error_message = 2;
    ServiceStats stats = 3;
}

// 服务统计
message ServiceStats {
    int64 total_requests = 1;
    int64 successful_requests = 2;
    int64 failed_requests = 3;
    double avg_response_time_ms = 4;
    double p95_response_time_ms = 5;
    double p99_response_time_ms = 6;
    int32 active_models = 7;
    double cache_hit_rate = 8;
    int64 uptime_seconds = 9;
    map<string, int64> model_request_counts = 10;
    map<string, double> model_avg_response_times = 11;
}
