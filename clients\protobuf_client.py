#!/usr/bin/env python3
"""
Protobuf工业控制客户端
支持JSON和Protobuf双协议的客户端实现
"""

import requests
import time
import json
import logging
from typing import Dict, Any, Optional

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 尝试导入Protobuf模块
try:
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    
    from DBUtil.industrial_control.v1 import shouwei_service_pb2
    from DBUtil.industrial_control.v1 import kongwen_service_pb2
    from DBUtil.industrial_control.v1 import common_pb2
    PROTOBUF_AVAILABLE = True
    logger.info("Protobuf模块加载成功")
except ImportError as e:
    PROTOBUF_AVAILABLE = False
    logger.warning(f"Protobuf模块不可用: {e}")

class ProtobufIndustrialClient:
    """Protobuf工业控制客户端"""
    
    def __init__(self, api_base_url: str, prefer_protobuf: bool = True):
        """
        初始化客户端
        
        Args:
            api_base_url: API基础URL
            prefer_protobuf: 是否优先使用Protobuf协议
        """
        self.api_base_url = api_base_url.rstrip('/')
        self.prefer_protobuf = prefer_protobuf and PROTOBUF_AVAILABLE
        self.session = requests.Session()
        
        # 设置默认超时
        self.session.timeout = 30
        
        # 协议统计
        self.stats = {
            'protobuf_requests': 0,
            'json_requests': 0,
            'protobuf_errors': 0,
            'json_fallbacks': 0
        }
        
        logger.info(f"客户端初始化完成，优先协议: {'Protobuf' if self.prefer_protobuf else 'JSON'}")
    
    def send_shouwei_prediction(self, device_id: str, sensor_data: Dict[str, Any], 
                               control_params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        发送首尾控制预测请求
        
        Args:
            device_id: 设备ID
            sensor_data: 传感器数据
            control_params: 控制参数
            
        Returns:
            预测结果字典
        """
        if self.prefer_protobuf:
            try:
                return self._send_shouwei_protobuf_request(device_id, sensor_data, control_params)
            except Exception as e:
                logger.warning(f"Protobuf请求失败，降级到JSON: {e}")
                self.stats['protobuf_errors'] += 1
                self.stats['json_fallbacks'] += 1
                return self._send_shouwei_json_request(device_id, sensor_data, control_params)
        else:
            return self._send_shouwei_json_request(device_id, sensor_data, control_params)
    
    def send_kongwen_prediction(self, device_id: str, sensor_data: Dict[str, Any],
                               control_params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        发送控温预测请求
        
        Args:
            device_id: 设备ID
            sensor_data: 传感器数据
            control_params: 控制参数
            
        Returns:
            预测结果字典
        """
        if self.prefer_protobuf:
            try:
                return self._send_kongwen_protobuf_request(device_id, sensor_data, control_params)
            except Exception as e:
                logger.warning(f"Protobuf请求失败，降级到JSON: {e}")
                self.stats['protobuf_errors'] += 1
                self.stats['json_fallbacks'] += 1
                return self._send_kongwen_json_request(device_id, sensor_data, control_params)
        else:
            return self._send_kongwen_json_request(device_id, sensor_data, control_params)
    
    def _send_shouwei_protobuf_request(self, device_id: str, sensor_data: Dict[str, Any],
                                      control_params: Dict[str, Any]) -> Dict[str, Any]:
        """发送首尾控制Protobuf请求"""
        if not PROTOBUF_AVAILABLE:
            raise ImportError("Protobuf不可用")
        
        # 创建Protobuf请求
        request = shouwei_service_pb2.ShouweiPredictionRequest()
        
        # 填充设备信息
        request.device_info.device_id = device_id
        request.device_info.device_type = "shouwei_lasu_control"
        request.device_info.location = sensor_data.get('location', 'unknown')
        
        # 填充传感器数据
        sensor = request.sensor_data
        sensor.temperature = sensor_data.get('temperature', 0.0)
        sensor.pressure = sensor_data.get('pressure', 0.0)
        sensor.flow_rate = sensor_data.get('flow_rate', 0.0)
        sensor.power_consumption = sensor_data.get('power_consumption', 0.0)
        sensor.humidity = sensor_data.get('humidity', 0.0)
        sensor.vibration = sensor_data.get('vibration', 0.0)
        
        # 填充控制参数
        control = request.control_parameters
        control.target_temperature = control_params.get('target_temperature', 25.0)
        control.max_power_limit = control_params.get('max_power_limit', 1000)
        control.control_precision = control_params.get('control_precision', 0.1)
        control.emergency_stop = control_params.get('emergency_stop', False)
        
        # 填充首尾控制特有数据
        shouwei_data = request.shouwei_data
        shouwei_data.lasu_coefficient = sensor_data.get('lasu_coefficient', 1.0)
        shouwei_data.baseline_power = sensor_data.get('baseline_power', 500.0)
        shouwei_data.weight_corrections.extend(sensor_data.get('weight_corrections', [1.0, 1.0, 1.0]))
        
        # 发送请求
        response = self.session.post(
            f"{self.api_base_url}/api/v1/shouwei/predict",
            data=request.SerializeToString(),
            headers={'Content-Type': 'application/x-protobuf'},
            timeout=30
        )
        
        if response.status_code == 200:
            # 解析Protobuf响应
            response_msg = shouwei_service_pb2.ShouweiPredictionResponse()
            response_msg.ParseFromString(response.content)
            
            self.stats['protobuf_requests'] += 1
            return self._protobuf_response_to_dict(response_msg)
        else:
            raise Exception(f"API请求失败: {response.status_code}, {response.text}")
    
    def _send_kongwen_protobuf_request(self, device_id: str, sensor_data: Dict[str, Any],
                                      control_params: Dict[str, Any]) -> Dict[str, Any]:
        """发送控温Protobuf请求"""
        if not PROTOBUF_AVAILABLE:
            raise ImportError("Protobuf不可用")
        
        # 创建Protobuf请求
        request = kongwen_service_pb2.KongwenPredictionRequest()
        
        # 填充设备信息
        request.device_info.device_id = device_id
        request.device_info.device_type = "kongwen_power_control"
        
        # 填充传感器数据
        sensor = request.sensor_data
        sensor.temperature = sensor_data.get('temperature', 0.0)
        sensor.pressure = sensor_data.get('pressure', 0.0)
        sensor.flow_rate = sensor_data.get('flow_rate', 0.0)
        sensor.power_consumption = sensor_data.get('power_consumption', 0.0)
        
        # 填充控制参数
        control = request.control_parameters
        control.target_temperature = control_params.get('target_temperature', 25.0)
        control.max_power_limit = control_params.get('max_power_limit', 1000)
        control.emergency_stop = control_params.get('emergency_stop', False)
        
        # 填充控温特有数据
        kongwen_data = request.kongwen_data
        kongwen_data.thermal_capacity = sensor_data.get('thermal_capacity', 1000.0)
        kongwen_data.heat_transfer_coefficient = sensor_data.get('heat_transfer_coeff', 10.0)
        kongwen_data.ambient_temperature = sensor_data.get('ambient_temp', 20.0)
        
        # 发送请求
        response = self.session.post(
            f"{self.api_base_url}/api/v1/kongwen/predict",
            data=request.SerializeToString(),
            headers={'Content-Type': 'application/x-protobuf'},
            timeout=30
        )
        
        if response.status_code == 200:
            # 解析Protobuf响应
            response_msg = kongwen_service_pb2.KongwenPredictionResponse()
            response_msg.ParseFromString(response.content)
            
            self.stats['protobuf_requests'] += 1
            return self._protobuf_response_to_dict(response_msg)
        else:
            raise Exception(f"API请求失败: {response.status_code}, {response.text}")
    
    def _send_shouwei_json_request(self, device_id: str, sensor_data: Dict[str, Any],
                                  control_params: Dict[str, Any]) -> Dict[str, Any]:
        """发送首尾控制JSON请求"""
        json_data = {
            'device_id': device_id,
            'timestamp': time.time(),
            'sensor_data': sensor_data,
            'control_parameters': control_params,
            'device_type': 'shouwei_lasu_control'
        }
        
        response = self.session.post(
            f"{self.api_base_url}/api/v1/predict",
            json=json_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            self.stats['json_requests'] += 1
            return response.json()
        else:
            raise Exception(f"JSON API请求失败: {response.status_code}, {response.text}")
    
    def _send_kongwen_json_request(self, device_id: str, sensor_data: Dict[str, Any],
                                  control_params: Dict[str, Any]) -> Dict[str, Any]:
        """发送控温JSON请求"""
        json_data = {
            'device_id': device_id,
            'timestamp': time.time(),
            'sensor_data': sensor_data,
            'control_parameters': control_params,
            'device_type': 'kongwen_power_control'
        }
        
        response = self.session.post(
            f"{self.api_base_url}/api/v1/predict",
            json=json_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            self.stats['json_requests'] += 1
            return response.json()
        else:
            raise Exception(f"JSON API请求失败: {response.status_code}, {response.text}")
    
    def _protobuf_response_to_dict(self, response_msg) -> Dict[str, Any]:
        """Protobuf响应转换为字典"""
        # 这里简化处理，实际应该使用protobuf的MessageToDict
        result = {
            'success': response_msg.success,
            'error_message': response_msg.error_message,
            'prediction': {
                'main_power': response_msg.prediction.main_power,
                'vice_power': response_msg.prediction.vice_power,
                'total_power': response_msg.prediction.total_power,
                'predicted_temperature': response_msg.prediction.predicted_temperature,
                'confidence': response_msg.prediction.confidence
            },
            'metadata': {
                'protocol': 'protobuf',
                'processing_time_ms': response_msg.prediction.metadata.processing_time_ms,
                'cache_hit': response_msg.prediction.metadata.cache_hit
            }
        }
        
        # 添加特定类型的结果
        if hasattr(response_msg, 'shouwei_result'):
            result['shouwei_result'] = {
                'lasu_efficiency': response_msg.shouwei_result.lasu_efficiency,
                'stability_index': response_msg.shouwei_result.stability_index,
                'adjusted_weights': list(response_msg.shouwei_result.adjusted_weights)
            }
        
        if hasattr(response_msg, 'kongwen_result'):
            result['kongwen_result'] = {
                'required_heating_power': response_msg.kongwen_result.required_heating_power,
                'required_cooling_power': response_msg.kongwen_result.required_cooling_power,
                'temperature_rise_rate': response_msg.kongwen_result.temperature_rise_rate,
                'settling_time': response_msg.kongwen_result.settling_time
            }
        
        return result
    
    def get_stats(self) -> Dict[str, Any]:
        """获取客户端统计信息"""
        total_requests = self.stats['protobuf_requests'] + self.stats['json_requests']
        
        return {
            'total_requests': total_requests,
            'protobuf_requests': self.stats['protobuf_requests'],
            'json_requests': self.stats['json_requests'],
            'protobuf_errors': self.stats['protobuf_errors'],
            'json_fallbacks': self.stats['json_fallbacks'],
            'protobuf_success_rate': (
                self.stats['protobuf_requests'] / (self.stats['protobuf_requests'] + self.stats['protobuf_errors'])
                if (self.stats['protobuf_requests'] + self.stats['protobuf_errors']) > 0 else 0
            ),
            'protobuf_adoption_rate': (
                self.stats['protobuf_requests'] / total_requests
                if total_requests > 0 else 0
            )
        }

# 使用示例
if __name__ == "__main__":
    # 创建客户端
    client = ProtobufIndustrialClient("http://localhost:5000", prefer_protobuf=True)
    
    # 首尾控制示例
    shouwei_sensor_data = {
        "temperature": 25.5,
        "pressure": 1.2,
        "flow_rate": 15.8,
        "power_consumption": 850,
        "lasu_coefficient": 1.25,
        "baseline_power": 500.0,
        "weight_corrections": [0.95, 1.02, 0.98, 1.01]
    }
    
    shouwei_control_params = {
        "target_temperature": 26.0,
        "max_power_limit": 1000,
        "control_precision": 0.1,
        "emergency_stop": False
    }
    
    try:
        result = client.send_shouwei_prediction("shouwei_device_001", shouwei_sensor_data, shouwei_control_params)
        print("首尾控制预测结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"首尾控制预测失败: {e}")
    
    # 控温示例
    kongwen_sensor_data = {
        "temperature": 24.8,
        "pressure": 1.1,
        "power_consumption": 750,
        "thermal_capacity": 1000.0,
        "heat_transfer_coeff": 10.0,
        "ambient_temp": 20.0
    }
    
    kongwen_control_params = {
        "target_temperature": 25.5,
        "max_power_limit": 800,
        "emergency_stop": False
    }
    
    try:
        result = client.send_kongwen_prediction("kongwen_device_001", kongwen_sensor_data, kongwen_control_params)
        print("\n控温预测结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"控温预测失败: {e}")
    
    # 显示统计信息
    print("\n客户端统计:")
    print(json.dumps(client.get_stats(), indent=2, ensure_ascii=False))
