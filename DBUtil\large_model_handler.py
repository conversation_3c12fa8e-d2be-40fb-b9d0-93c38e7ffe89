#!/usr/bin/env python3
"""
大模型处理器
为大于10MB的模型文件实现分块传输、进度监控和增量更新
"""

import hashlib
import logging
import time
import threading
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum

from .serialization_manager import serialization_manager

logger = logging.getLogger(__name__)

class TransferStatus(Enum):
    """传输状态"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class ChunkInfo:
    """分块信息"""
    chunk_id: int
    offset: int
    size: int
    checksum: str
    data: bytes

@dataclass
class TransferProgress:
    """传输进度"""
    total_size: int
    transferred_size: int
    chunks_total: int
    chunks_completed: int
    start_time: float
    status: TransferStatus
    error_message: Optional[str] = None

class LargeModelHandler:
    """大模型处理器"""
    
    def __init__(self, 
                 chunk_size: int = 1024 * 1024,  # 1MB chunks
                 max_chunks_in_memory: int = 10,
                 timeout_seconds: int = 300,
                 enable_compression: bool = True):
        """
        初始化大模型处理器
        
        Args:
            chunk_size: 分块大小（字节）
            max_chunks_in_memory: 内存中最大分块数
            timeout_seconds: 传输超时时间
            enable_compression: 是否启用压缩
        """
        self.chunk_size = chunk_size
        self.max_chunks_in_memory = max_chunks_in_memory
        self.timeout_seconds = timeout_seconds
        self.enable_compression = enable_compression
        
        # 传输状态跟踪
        self.active_transfers: Dict[str, TransferProgress] = {}
        self.transfer_lock = threading.RLock()
        
        # 分块缓存
        self.chunk_cache: Dict[str, List[ChunkInfo]] = {}
        self.cache_lock = threading.RLock()
    
    def serialize_large_model(self, 
                            model_data: Any, 
                            model_id: str,
                            progress_callback: Optional[Callable[[TransferProgress], None]] = None) -> List[ChunkInfo]:
        """
        序列化大模型为分块数据
        
        Args:
            model_data: 模型数据
            model_id: 模型唯一标识
            progress_callback: 进度回调函数
            
        Returns:
            分块信息列表
        """
        try:
            # 序列化模型数据
            serialized_data = serialization_manager.serialize(model_data)
            total_size = len(serialized_data)
            
            # 检查是否需要分块
            if total_size <= self.chunk_size:
                logger.info(f"模型 {model_id} 大小 {total_size} 字节，无需分块")
                return [ChunkInfo(
                    chunk_id=0,
                    offset=0,
                    size=total_size,
                    checksum=self._calculate_checksum(serialized_data),
                    data=serialized_data
                )]
            
            # 初始化传输进度
            chunks_total = (total_size + self.chunk_size - 1) // self.chunk_size
            progress = TransferProgress(
                total_size=total_size,
                transferred_size=0,
                chunks_total=chunks_total,
                chunks_completed=0,
                start_time=time.time(),
                status=TransferStatus.IN_PROGRESS
            )
            
            with self.transfer_lock:
                self.active_transfers[model_id] = progress
            
            logger.info(f"开始分块序列化模型 {model_id}，总大小: {total_size} 字节，分块数: {chunks_total}")
            
            # 分块处理
            chunks = []
            for chunk_id in range(chunks_total):
                offset = chunk_id * self.chunk_size
                chunk_size = min(self.chunk_size, total_size - offset)
                chunk_data = serialized_data[offset:offset + chunk_size]
                
                chunk_info = ChunkInfo(
                    chunk_id=chunk_id,
                    offset=offset,
                    size=chunk_size,
                    checksum=self._calculate_checksum(chunk_data),
                    data=chunk_data
                )
                chunks.append(chunk_info)
                
                # 更新进度
                progress.chunks_completed += 1
                progress.transferred_size += chunk_size
                
                # 调用进度回调
                if progress_callback:
                    progress_callback(progress)
                
                # 检查超时
                if time.time() - progress.start_time > self.timeout_seconds:
                    progress.status = TransferStatus.FAILED
                    progress.error_message = "序列化超时"
                    raise TimeoutError("序列化超时")
            
            # 完成传输
            progress.status = TransferStatus.COMPLETED
            
            # 缓存分块信息
            with self.cache_lock:
                self.chunk_cache[model_id] = chunks
            
            logger.info(f"模型 {model_id} 分块序列化完成，耗时: {time.time() - progress.start_time:.2f} 秒")
            return chunks
            
        except Exception as e:
            # 更新错误状态
            if model_id in self.active_transfers:
                self.active_transfers[model_id].status = TransferStatus.FAILED
                self.active_transfers[model_id].error_message = str(e)
            
            logger.error(f"模型 {model_id} 序列化失败: {e}")
            raise
    
    def deserialize_large_model(self, 
                               chunks: List[ChunkInfo], 
                               model_id: str,
                               progress_callback: Optional[Callable[[TransferProgress], None]] = None) -> Any:
        """
        从分块数据反序列化大模型
        
        Args:
            chunks: 分块信息列表
            model_id: 模型唯一标识
            progress_callback: 进度回调函数
            
        Returns:
            反序列化的模型数据
        """
        try:
            # 验证分块完整性
            self._validate_chunks(chunks)
            
            # 初始化传输进度
            total_size = sum(chunk.size for chunk in chunks)
            progress = TransferProgress(
                total_size=total_size,
                transferred_size=0,
                chunks_total=len(chunks),
                chunks_completed=0,
                start_time=time.time(),
                status=TransferStatus.IN_PROGRESS
            )
            
            with self.transfer_lock:
                self.active_transfers[model_id] = progress
            
            logger.info(f"开始反序列化模型 {model_id}，总大小: {total_size} 字节，分块数: {len(chunks)}")
            
            # 按顺序重组数据
            chunks.sort(key=lambda x: x.chunk_id)
            reconstructed_data = bytearray()
            
            for chunk in chunks:
                # 验证分块校验和
                if self._calculate_checksum(chunk.data) != chunk.checksum:
                    raise ValueError(f"分块 {chunk.chunk_id} 校验和验证失败")
                
                reconstructed_data.extend(chunk.data)
                
                # 更新进度
                progress.chunks_completed += 1
                progress.transferred_size += chunk.size
                
                # 调用进度回调
                if progress_callback:
                    progress_callback(progress)
                
                # 检查超时
                if time.time() - progress.start_time > self.timeout_seconds:
                    progress.status = TransferStatus.FAILED
                    progress.error_message = "反序列化超时"
                    raise TimeoutError("反序列化超时")
            
            # 反序列化重组后的数据
            model_data = serialization_manager.deserialize(bytes(reconstructed_data))
            
            # 完成传输
            progress.status = TransferStatus.COMPLETED
            
            logger.info(f"模型 {model_id} 反序列化完成，耗时: {time.time() - progress.start_time:.2f} 秒")
            return model_data
            
        except Exception as e:
            # 更新错误状态
            if model_id in self.active_transfers:
                self.active_transfers[model_id].status = TransferStatus.FAILED
                self.active_transfers[model_id].error_message = str(e)
            
            logger.error(f"模型 {model_id} 反序列化失败: {e}")
            raise
    
    def calculate_model_diff(self, old_model: Any, new_model: Any) -> Dict[str, Any]:
        """
        计算模型差异（增量更新）
        
        Args:
            old_model: 旧模型数据
            new_model: 新模型数据
            
        Returns:
            模型差异数据
        """
        try:
            # 序列化两个模型
            old_serialized = serialization_manager.serialize(old_model)
            new_serialized = serialization_manager.serialize(new_model)
            
            # 简单的差异计算（实际应用中可以使用更复杂的算法）
            if old_serialized == new_serialized:
                return {'type': 'no_change', 'size': 0}
            
            # 计算差异大小
            diff_size = len(new_serialized)
            compression_ratio = diff_size / len(old_serialized) if len(old_serialized) > 0 else 1.0
            
            return {
                'type': 'full_update',  # 简化实现，实际可以实现增量差异
                'old_size': len(old_serialized),
                'new_size': len(new_serialized),
                'diff_size': diff_size,
                'compression_ratio': compression_ratio,
                'data': new_serialized
            }
            
        except Exception as e:
            logger.error(f"计算模型差异失败: {e}")
            raise
    
    def apply_model_diff(self, old_model: Any, diff_data: Dict[str, Any]) -> Any:
        """
        应用模型差异（增量更新）
        
        Args:
            old_model: 旧模型数据
            diff_data: 差异数据
            
        Returns:
            更新后的模型数据
        """
        try:
            if diff_data['type'] == 'no_change':
                return old_model
            elif diff_data['type'] == 'full_update':
                return serialization_manager.deserialize(diff_data['data'])
            else:
                raise ValueError(f"未知的差异类型: {diff_data['type']}")
                
        except Exception as e:
            logger.error(f"应用模型差异失败: {e}")
            raise
    
    def get_transfer_progress(self, model_id: str) -> Optional[TransferProgress]:
        """获取传输进度"""
        with self.transfer_lock:
            return self.active_transfers.get(model_id)
    
    def cancel_transfer(self, model_id: str) -> bool:
        """取消传输"""
        with self.transfer_lock:
            if model_id in self.active_transfers:
                self.active_transfers[model_id].status = TransferStatus.CANCELLED
                return True
            return False
    
    def cleanup_completed_transfers(self, max_age_seconds: int = 3600):
        """清理已完成的传输记录"""
        current_time = time.time()
        
        with self.transfer_lock:
            completed_transfers = [
                model_id for model_id, progress in self.active_transfers.items()
                if progress.status in [TransferStatus.COMPLETED, TransferStatus.FAILED, TransferStatus.CANCELLED]
                and (current_time - progress.start_time) > max_age_seconds
            ]
            
            for model_id in completed_transfers:
                del self.active_transfers[model_id]
                
                # 清理缓存
                with self.cache_lock:
                    if model_id in self.chunk_cache:
                        del self.chunk_cache[model_id]
        
        if completed_transfers:
            logger.info(f"清理了 {len(completed_transfers)} 个已完成的传输记录")
    
    def _calculate_checksum(self, data: bytes) -> str:
        """计算数据校验和"""
        return hashlib.md5(data).hexdigest()
    
    def _validate_chunks(self, chunks: List[ChunkInfo]):
        """验证分块完整性"""
        if not chunks:
            raise ValueError("分块列表为空")
        
        # 检查分块ID连续性
        chunk_ids = sorted([chunk.chunk_id for chunk in chunks])
        expected_ids = list(range(len(chunks)))
        
        if chunk_ids != expected_ids:
            raise ValueError(f"分块ID不连续: 期望 {expected_ids}, 实际 {chunk_ids}")
        
        # 检查偏移量和大小
        chunks.sort(key=lambda x: x.chunk_id)
        expected_offset = 0
        
        for chunk in chunks:
            if chunk.offset != expected_offset:
                raise ValueError(f"分块 {chunk.chunk_id} 偏移量错误: 期望 {expected_offset}, 实际 {chunk.offset}")
            
            if len(chunk.data) != chunk.size:
                raise ValueError(f"分块 {chunk.chunk_id} 大小不匹配: 期望 {chunk.size}, 实际 {len(chunk.data)}")
            
            expected_offset += chunk.size
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取处理器统计信息"""
        with self.transfer_lock:
            active_count = len([p for p in self.active_transfers.values() if p.status == TransferStatus.IN_PROGRESS])
            completed_count = len([p for p in self.active_transfers.values() if p.status == TransferStatus.COMPLETED])
            failed_count = len([p for p in self.active_transfers.values() if p.status == TransferStatus.FAILED])
        
        with self.cache_lock:
            cached_models = len(self.chunk_cache)
        
        return {
            'active_transfers': active_count,
            'completed_transfers': completed_count,
            'failed_transfers': failed_count,
            'cached_models': cached_models,
            'chunk_size': self.chunk_size,
            'max_chunks_in_memory': self.max_chunks_in_memory,
            'timeout_seconds': self.timeout_seconds
        }

# 全局大模型处理器实例
large_model_handler = LargeModelHandler()
