#!/usr/bin/env python3
"""
Protobuf序列化管理器
扩展现有序列化管理器，支持Protocol Buffers
"""

import pickle
import logging
import time
import threading
from typing import Any, Dict, Optional, Type
from enum import Enum

from .serialization_manager import SerializationManager, SerializationMethod

# 尝试导入Protobuf库
try:
    import google.protobuf.message
    from google.protobuf.json_format import MessageToDict, Parse
    import lz4.frame
    PROTOBUF_AVAILABLE = True
except ImportError:
    PROTOBUF_AVAILABLE = False
    logging.warning("Protobuf库未安装，Protobuf功能不可用")

logger = logging.getLogger(__name__)

class ExtendedSerializationMethod(Enum):
    """扩展的序列化方法枚举"""
    PICKLE = "pickle"
    MSGPACK = "msgpack"
    MSGPACK_LZ4 = "msgpack_lz4"
    PROTOBUF = "protobuf"
    PROTOBUF_LZ4 = "protobuf_lz4"

class ProtobufSerializationManager:
    """
    增强的序列化管理器，支持Protobuf
    """
    
    def __init__(self, 
                 default_method: ExtendedSerializationMethod = ExtendedSerializationMethod.PROTOBUF_LZ4,
                 enable_fallback: bool = True,
                 enable_stats: bool = True):
        """
        初始化Protobuf序列化管理器
        
        Args:
            default_method: 默认序列化方法
            enable_fallback: 是否启用降级机制
            enable_stats: 是否启用性能统计
        """
        self.default_method = default_method
        self.enable_fallback = enable_fallback
        self.enable_stats = enable_stats
        
        # 原始序列化管理器（处理非Protobuf格式）
        self.base_manager = SerializationManager(
            default_method=SerializationMethod.MSGPACK_LZ4,
            enable_fallback=enable_fallback,
            enable_stats=enable_stats
        )
        
        # Protobuf类型注册表
        self.protobuf_registry: Dict[str, Any] = {}
        
        # 性能统计
        self.stats = {
            'protobuf_serialize_count': 0,
            'protobuf_deserialize_count': 0,
            'protobuf_serialize_time_total': 0.0,
            'protobuf_deserialize_time_total': 0.0,
            'protobuf_fallback_count': 0,
            'protobuf_error_count': 0
        }
        self.stats_lock = threading.RLock()
    
    def register_protobuf_type(self, type_name: str, message_class: Any):
        """注册Protobuf消息类型"""
        if not PROTOBUF_AVAILABLE:
            logger.warning("Protobuf不可用，无法注册类型")
            return
        
        self.protobuf_registry[type_name] = message_class
        logger.info(f"注册Protobuf类型: {type_name} -> {message_class.__name__}")
    
    def serialize(self, data: Any, method: Optional[ExtendedSerializationMethod] = None, 
                  protobuf_type: Optional[str] = None) -> bytes:
        """
        统一序列化接口
        
        Args:
            data: 要序列化的数据
            method: 序列化方法
            protobuf_type: Protobuf类型名称（用于从Python对象创建Protobuf消息）
            
        Returns:
            序列化后的字节数据
        """
        method = method or self.default_method
        
        try:
            if method in [ExtendedSerializationMethod.PROTOBUF, ExtendedSerializationMethod.PROTOBUF_LZ4]:
                return self._serialize_protobuf(data, method, protobuf_type)
            else:
                # 使用原始序列化管理器
                base_method = SerializationMethod(method.value)
                return self.base_manager.serialize(data, base_method)
                
        except Exception as e:
            if self.enable_fallback:
                logger.warning(f"序列化失败，降级到Pickle: {e}")
                with self.stats_lock:
                    self.stats['protobuf_fallback_count'] += 1
                return pickle.dumps(data)
            else:
                with self.stats_lock:
                    self.stats['protobuf_error_count'] += 1
                raise e
    
    def deserialize(self, data: bytes, method: Optional[ExtendedSerializationMethod] = None,
                   protobuf_type: Optional[str] = None) -> Any:
        """
        统一反序列化接口
        
        Args:
            data: 序列化的字节数据
            method: 序列化方法（如果为None则自动检测）
            protobuf_type: Protobuf类型名称
            
        Returns:
            反序列化后的数据
        """
        if method is None:
            method = self._detect_format(data)
        
        try:
            if method in [ExtendedSerializationMethod.PROTOBUF, ExtendedSerializationMethod.PROTOBUF_LZ4]:
                return self._deserialize_protobuf(data, method, protobuf_type)
            else:
                # 使用原始序列化管理器
                base_method = SerializationMethod(method.value)
                return self.base_manager.deserialize(data, base_method)
                
        except Exception as e:
            if self.enable_fallback:
                logger.warning(f"反序列化失败，尝试Pickle: {e}")
                with self.stats_lock:
                    self.stats['protobuf_fallback_count'] += 1
                return pickle.loads(data)
            else:
                with self.stats_lock:
                    self.stats['protobuf_error_count'] += 1
                raise e
    
    def _serialize_protobuf(self, data: Any, method: ExtendedSerializationMethod, 
                           protobuf_type: Optional[str] = None) -> bytes:
        """Protobuf序列化"""
        if not PROTOBUF_AVAILABLE:
            raise ImportError("Protobuf库不可用")
        
        start_time = time.time()
        
        try:
            if isinstance(data, google.protobuf.message.Message):
                # 直接序列化Protobuf消息
                serialized = data.SerializeToString()
            elif protobuf_type and protobuf_type in self.protobuf_registry:
                # 从Python对象创建Protobuf消息
                message_class = self.protobuf_registry[protobuf_type]
                message = self._python_to_protobuf(data, message_class)
                serialized = message.SerializeToString()
            else:
                raise ValueError(f"无法序列化数据类型: {type(data)}")
            
            if method == ExtendedSerializationMethod.PROTOBUF_LZ4:
                # 添加LZ4压缩
                serialized = lz4.frame.compress(serialized)
            
            # 更新统计
            if self.enable_stats:
                with self.stats_lock:
                    self.stats['protobuf_serialize_count'] += 1
                    self.stats['protobuf_serialize_time_total'] += time.time() - start_time
            
            return serialized
            
        except Exception as e:
            logger.error(f"Protobuf序列化失败: {e}")
            raise e
    
    def _deserialize_protobuf(self, data: bytes, method: ExtendedSerializationMethod,
                             protobuf_type: Optional[str] = None) -> Any:
        """Protobuf反序列化"""
        if not PROTOBUF_AVAILABLE:
            raise ImportError("Protobuf库不可用")
        
        start_time = time.time()
        
        try:
            if method == ExtendedSerializationMethod.PROTOBUF_LZ4:
                # LZ4解压缩
                data = lz4.frame.decompress(data)
            
            if protobuf_type and protobuf_type in self.protobuf_registry:
                message_class = self.protobuf_registry[protobuf_type]
                message = message_class()
                message.ParseFromString(data)
                
                # 更新统计
                if self.enable_stats:
                    with self.stats_lock:
                        self.stats['protobuf_deserialize_count'] += 1
                        self.stats['protobuf_deserialize_time_total'] += time.time() - start_time
                
                return message
            else:
                # 返回原始字节数据，由调用者处理
                return data
                
        except Exception as e:
            logger.error(f"Protobuf反序列化失败: {e}")
            raise e
    
    def _python_to_protobuf(self, data: Any, message_class: Any) -> Any:
        """Python对象转换为Protobuf消息"""
        import json
        
        # 使用JSON作为中间格式进行转换
        json_str = json.dumps(data, default=str)
        message = message_class()
        Parse(json_str, message)
        return message
    
    def _detect_format(self, data: bytes) -> ExtendedSerializationMethod:
        """检测数据格式"""
        if not data:
            return ExtendedSerializationMethod.PROTOBUF
        
        # LZ4 + Protobuf检测
        if data.startswith(b'\x04"M\x18'):
            try:
                decompressed = lz4.frame.decompress(data)
                if self._is_protobuf_data(decompressed):
                    return ExtendedSerializationMethod.PROTOBUF_LZ4
            except:
                pass
        
        # 纯Protobuf检测
        if self._is_protobuf_data(data):
            return ExtendedSerializationMethod.PROTOBUF
        
        # 降级到原有检测逻辑
        base_method = self.base_manager._detect_format(data)
        return ExtendedSerializationMethod(base_method.value)
    
    def _is_protobuf_data(self, data: bytes) -> bool:
        """检测是否为Protobuf数据"""
        try:
            if len(data) < 2:
                return False
            
            # 简单的Protobuf格式检测
            first_byte = data[0]
            # Protobuf字段编号通常在1-15范围内，wire type为0-5
            field_number = first_byte >> 3
            wire_type = first_byte & 0x07
            
            return 1 <= field_number <= 15 and 0 <= wire_type <= 5
        except:
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        with self.stats_lock:
            base_stats = self.base_manager.get_stats()
            
            # 计算平均时间
            avg_protobuf_serialize_time = (
                self.stats['protobuf_serialize_time_total'] / self.stats['protobuf_serialize_count']
                if self.stats['protobuf_serialize_count'] > 0 else 0
            )
            
            avg_protobuf_deserialize_time = (
                self.stats['protobuf_deserialize_time_total'] / self.stats['protobuf_deserialize_count']
                if self.stats['protobuf_deserialize_count'] > 0 else 0
            )
            
            protobuf_stats = {
                'protobuf_available': PROTOBUF_AVAILABLE,
                'protobuf_serialize_count': self.stats['protobuf_serialize_count'],
                'protobuf_deserialize_count': self.stats['protobuf_deserialize_count'],
                'avg_protobuf_serialize_time_ms': avg_protobuf_serialize_time * 1000,
                'avg_protobuf_deserialize_time_ms': avg_protobuf_deserialize_time * 1000,
                'protobuf_fallback_count': self.stats['protobuf_fallback_count'],
                'protobuf_error_count': self.stats['protobuf_error_count'],
                'registered_protobuf_types': list(self.protobuf_registry.keys())
            }
            
            # 合并统计信息
            return {**base_stats, **protobuf_stats}

# 全局Protobuf序列化管理器实例
protobuf_serialization_manager = ProtobufSerializationManager()
