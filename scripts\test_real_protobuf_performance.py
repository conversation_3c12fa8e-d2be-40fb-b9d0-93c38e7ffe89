#!/usr/bin/env python3
"""
真正的Protobuf性能测试
对比JSON、MessagePack和真正的Protobuf性能
"""

import sys
import os
import time
import json
import pickle

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def test_protobuf_vs_others():
    """测试Protobuf vs 其他序列化方法"""
    print("🚀 真正的Protobuf性能测试")
    print("="*50)
    
    # 导入真正的Protobuf
    from DBUtil.industrial_control.v1 import shouwei_service_pb2
    from DBUtil.protobuf_serializer import protobuf_serialization_manager, ExtendedSerializationMethod
    
    # 创建测试数据
    # 1. Protobuf消息
    protobuf_request = shouwei_service_pb2.ShouweiPredictionRequest()
    protobuf_request.device_info.device_id = "shouwei_performance_test"
    protobuf_request.device_info.device_type = "shouwei_lasu_control"
    protobuf_request.device_info.location = "production_line_1"
    
    protobuf_request.sensor_data.temperature = 25.5
    protobuf_request.sensor_data.pressure = 1.2
    protobuf_request.sensor_data.flow_rate = 15.8
    protobuf_request.sensor_data.power_consumption = 850.0
    protobuf_request.sensor_data.humidity = 65.2
    protobuf_request.sensor_data.vibration = 0.05
    
    protobuf_request.control_parameters.target_temperature = 26.0
    protobuf_request.control_parameters.max_power_limit = 1000
    protobuf_request.control_parameters.control_precision = 0.1
    protobuf_request.control_parameters.emergency_stop = False
    
    protobuf_request.shouwei_data.lasu_coefficient = 1.25
    protobuf_request.shouwei_data.baseline_power = 500.0
    protobuf_request.shouwei_data.weight_corrections.extend([0.95, 1.02, 0.98, 1.01, 0.99])
    
    # 2. 等价的Python字典（用于其他序列化方法）
    dict_data = {
        'device_info': {
            'device_id': 'shouwei_performance_test',
            'device_type': 'shouwei_lasu_control',
            'location': 'production_line_1'
        },
        'sensor_data': {
            'temperature': 25.5,
            'pressure': 1.2,
            'flow_rate': 15.8,
            'power_consumption': 850.0,
            'humidity': 65.2,
            'vibration': 0.05
        },
        'control_parameters': {
            'target_temperature': 26.0,
            'max_power_limit': 1000,
            'control_precision': 0.1,
            'emergency_stop': False
        },
        'shouwei_data': {
            'lasu_coefficient': 1.25,
            'baseline_power': 500.0,
            'weight_corrections': [0.95, 1.02, 0.98, 1.01, 0.99]
        }
    }
    
    iterations = 5000  # 更多迭代以获得准确结果
    
    print(f"测试数据复杂度: 嵌套字典，{len(str(dict_data))} 字符")
    print(f"迭代次数: {iterations}")
    print()
    
    results = {}
    
    # 测试方法列表
    test_methods = [
        ('JSON', test_json_performance),
        ('Pickle', test_pickle_performance),
        ('MessagePack', test_msgpack_performance),
        ('MessagePack+LZ4', test_msgpack_lz4_performance),
        ('真正的Protobuf', test_real_protobuf_performance)
    ]
    
    for method_name, test_func in test_methods:
        try:
            print(f"测试 {method_name}...")
            
            if method_name == '真正的Protobuf':
                result = test_func(protobuf_request, iterations)
            else:
                result = test_func(dict_data, iterations)
            
            results[method_name] = result
            
            print(f"  序列化时间: {result['serialize_time']:.3f}s")
            print(f"  反序列化时间: {result['deserialize_time']:.3f}s")
            print(f"  数据大小: {result['data_size']} bytes")
            print(f"  序列化速度: {result['serialize_ops_per_sec']:.0f} ops/s")
            print()
            
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
            results[method_name] = {'error': str(e)}
    
    # 输出对比结果
    print_comparison_results(results)
    
    return results

def test_json_performance(data, iterations):
    """测试JSON性能"""
    # 序列化测试
    start_time = time.time()
    for _ in range(iterations):
        serialized = json.dumps(data).encode('utf-8')
    serialize_time = time.time() - start_time
    
    # 反序列化测试
    start_time = time.time()
    for _ in range(iterations):
        deserialized = json.loads(serialized.decode('utf-8'))
    deserialize_time = time.time() - start_time
    
    return {
        'serialize_time': serialize_time,
        'deserialize_time': deserialize_time,
        'total_time': serialize_time + deserialize_time,
        'data_size': len(serialized),
        'serialize_ops_per_sec': iterations / serialize_time,
        'deserialize_ops_per_sec': iterations / deserialize_time
    }

def test_pickle_performance(data, iterations):
    """测试Pickle性能"""
    # 序列化测试
    start_time = time.time()
    for _ in range(iterations):
        serialized = pickle.dumps(data)
    serialize_time = time.time() - start_time
    
    # 反序列化测试
    start_time = time.time()
    for _ in range(iterations):
        deserialized = pickle.loads(serialized)
    deserialize_time = time.time() - start_time
    
    return {
        'serialize_time': serialize_time,
        'deserialize_time': deserialize_time,
        'total_time': serialize_time + deserialize_time,
        'data_size': len(serialized),
        'serialize_ops_per_sec': iterations / serialize_time,
        'deserialize_ops_per_sec': iterations / deserialize_time
    }

def test_msgpack_performance(data, iterations):
    """测试MessagePack性能"""
    try:
        import msgpack
    except ImportError:
        raise ImportError("MessagePack不可用")
    
    # 序列化测试
    start_time = time.time()
    for _ in range(iterations):
        serialized = msgpack.packb(data)
    serialize_time = time.time() - start_time
    
    # 反序列化测试
    start_time = time.time()
    for _ in range(iterations):
        deserialized = msgpack.unpackb(serialized, raw=False)
    deserialize_time = time.time() - start_time
    
    return {
        'serialize_time': serialize_time,
        'deserialize_time': deserialize_time,
        'total_time': serialize_time + deserialize_time,
        'data_size': len(serialized),
        'serialize_ops_per_sec': iterations / serialize_time,
        'deserialize_ops_per_sec': iterations / deserialize_time
    }

def test_msgpack_lz4_performance(data, iterations):
    """测试MessagePack+LZ4性能"""
    try:
        import msgpack
        import lz4.frame
    except ImportError:
        raise ImportError("MessagePack或LZ4不可用")
    
    # 序列化测试
    start_time = time.time()
    for _ in range(iterations):
        packed = msgpack.packb(data)
        serialized = lz4.frame.compress(packed)
    serialize_time = time.time() - start_time
    
    # 反序列化测试
    start_time = time.time()
    for _ in range(iterations):
        decompressed = lz4.frame.decompress(serialized)
        deserialized = msgpack.unpackb(decompressed, raw=False)
    deserialize_time = time.time() - start_time
    
    return {
        'serialize_time': serialize_time,
        'deserialize_time': deserialize_time,
        'total_time': serialize_time + deserialize_time,
        'data_size': len(serialized),
        'serialize_ops_per_sec': iterations / serialize_time,
        'deserialize_ops_per_sec': iterations / deserialize_time
    }

def test_real_protobuf_performance(protobuf_msg, iterations):
    """测试真正的Protobuf性能"""
    from DBUtil.industrial_control.v1 import shouwei_service_pb2
    
    # 序列化测试
    start_time = time.time()
    for _ in range(iterations):
        serialized = protobuf_msg.SerializeToString()
    serialize_time = time.time() - start_time
    
    # 反序列化测试
    start_time = time.time()
    for _ in range(iterations):
        new_msg = shouwei_service_pb2.ShouweiPredictionRequest()
        new_msg.ParseFromString(serialized)
    deserialize_time = time.time() - start_time
    
    return {
        'serialize_time': serialize_time,
        'deserialize_time': deserialize_time,
        'total_time': serialize_time + deserialize_time,
        'data_size': len(serialized),
        'serialize_ops_per_sec': iterations / serialize_time,
        'deserialize_ops_per_sec': iterations / deserialize_time
    }

def print_comparison_results(results):
    """打印对比结果"""
    print("🏆 性能对比结果")
    print("="*80)
    print(f"{'方法':<15} {'序列化时间':<12} {'反序列化时间':<14} {'数据大小':<10} {'总时间':<10} {'序列化速度':<12}")
    print("-"*80)
    
    for method, result in results.items():
        if 'error' not in result:
            print(f"{method:<15} {result['serialize_time']:<12.3f} "
                  f"{result['deserialize_time']:<14.3f} {result['data_size']:<10} "
                  f"{result['total_time']:<10.3f} {result['serialize_ops_per_sec']:<12.0f}")
        else:
            print(f"{method:<15} ERROR: {result['error']}")
    
    # 计算改进幅度
    if 'JSON' in results and '真正的Protobuf' in results:
        json_result = results['JSON']
        protobuf_result = results['真正的Protobuf']
        
        if 'error' not in json_result and 'error' not in protobuf_result:
            size_improvement = (json_result['data_size'] - protobuf_result['data_size']) / json_result['data_size'] * 100
            time_improvement = (json_result['total_time'] - protobuf_result['total_time']) / json_result['total_time'] * 100
            speed_improvement = (protobuf_result['serialize_ops_per_sec'] - json_result['serialize_ops_per_sec']) / json_result['serialize_ops_per_sec'] * 100
            
            print(f"\n🎯 Protobuf 相对于 JSON 的改进:")
            print(f"  数据大小减少: {size_improvement:.1f}%")
            print(f"  处理时间减少: {time_improvement:.1f}%")
            print(f"  序列化速度提升: {speed_improvement:.1f}%")
    
    # 找出最佳方法
    valid_results = {k: v for k, v in results.items() if 'error' not in v}
    if valid_results:
        best_size = min(valid_results.items(), key=lambda x: x[1]['data_size'])
        best_speed = max(valid_results.items(), key=lambda x: x[1]['serialize_ops_per_sec'])
        
        print(f"\n🏅 最佳性能:")
        print(f"  最小数据大小: {best_size[0]} ({best_size[1]['data_size']} bytes)")
        print(f"  最快序列化: {best_speed[0]} ({best_speed[1]['serialize_ops_per_sec']:.0f} ops/s)")

def main():
    """主函数"""
    try:
        results = test_protobuf_vs_others()
        
        print("\n" + "="*50)
        print("🎉 测试完成！")
        print("\n💡 结论:")
        print("  • Protobuf在数据大小方面通常最优")
        print("  • 序列化速度取决于数据复杂度")
        print("  • 对于工业控制系统，建议使用Protobuf")
        print("  • 可以根据具体场景选择最适合的方法")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
