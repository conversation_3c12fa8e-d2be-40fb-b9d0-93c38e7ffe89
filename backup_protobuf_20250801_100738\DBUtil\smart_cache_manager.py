#!/usr/bin/env python3
"""
智能缓存管理器
实现基于访问模式的智能TTL和缓存预热机制
"""

import time
import threading
import logging
import json
from typing import Dict, Any, Optional, List, Tuple
from collections import defaultdict, deque
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class CacheLevel(Enum):
    """缓存级别"""
    MEMORY = "memory"      # 内存缓存
    SQLITE = "sqlite"      # SQLite缓存
    REDIS = "redis"        # Redis缓存

@dataclass
class AccessPattern:
    """访问模式数据类"""
    device_id: str
    access_count: int = 0
    last_access_time: float = 0.0
    access_frequency: float = 0.0  # 每小时访问次数
    avg_interval: float = 0.0      # 平均访问间隔
    access_times: deque = None     # 最近访问时间队列
    
    def __post_init__(self):
        if self.access_times is None:
            self.access_times = deque(maxlen=100)  # 保留最近100次访问记录

class SmartCacheManager:
    """智能缓存管理器"""
    
    def __init__(self, 
                 base_ttl: int = 180,           # 基础TTL（秒）
                 min_ttl: int = 60,             # 最小TTL
                 max_ttl: int = 3600,           # 最大TTL
                 preload_threshold: float = 5.0, # 预加载阈值（每小时访问次数）
                 pattern_window: int = 3600):    # 访问模式分析窗口（秒）
        
        self.base_ttl = base_ttl
        self.min_ttl = min_ttl
        self.max_ttl = max_ttl
        self.preload_threshold = preload_threshold
        self.pattern_window = pattern_window
        
        # 访问模式跟踪
        self.access_patterns: Dict[str, AccessPattern] = {}
        self.pattern_lock = threading.RLock()
        
        # 缓存统计
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'preloads': 0,
            'ttl_adjustments': 0,
            'total_requests': 0
        }
        
        # 预加载队列
        self.preload_queue: List[Tuple[str, str, str]] = []  # (model_name, device_id, version)
        self.preload_lock = threading.Lock()
        
        # 启动后台任务
        self._start_background_tasks()
    
    def record_access(self, device_id: str, cache_hit: bool = True) -> None:
        """记录访问模式"""
        current_time = time.time()
        
        with self.pattern_lock:
            if device_id not in self.access_patterns:
                self.access_patterns[device_id] = AccessPattern(device_id=device_id)
            
            pattern = self.access_patterns[device_id]
            pattern.access_count += 1
            pattern.access_times.append(current_time)
            pattern.last_access_time = current_time
            
            # 更新访问频率
            self._update_access_frequency(pattern)
            
            # 更新统计
            if cache_hit:
                self.cache_stats['hits'] += 1
            else:
                self.cache_stats['misses'] += 1
            self.cache_stats['total_requests'] += 1
    
    def _update_access_frequency(self, pattern: AccessPattern) -> None:
        """更新访问频率"""
        current_time = time.time()
        
        # 过滤时间窗口内的访问记录
        window_start = current_time - self.pattern_window
        recent_accesses = [t for t in pattern.access_times if t >= window_start]
        
        if len(recent_accesses) >= 2:
            # 计算访问频率（每小时）
            time_span = recent_accesses[-1] - recent_accesses[0]
            if time_span > 0:
                pattern.access_frequency = len(recent_accesses) / (time_span / 3600)
                
                # 计算平均访问间隔
                intervals = [recent_accesses[i] - recent_accesses[i-1] 
                           for i in range(1, len(recent_accesses))]
                pattern.avg_interval = sum(intervals) / len(intervals)
    
    def calculate_smart_ttl(self, device_id: str) -> int:
        """计算智能TTL"""
        with self.pattern_lock:
            if device_id not in self.access_patterns:
                return self.base_ttl
            
            pattern = self.access_patterns[device_id]
            
            # 基于访问频率调整TTL
            if pattern.access_frequency > 0:
                # 高频访问 -> 长TTL，低频访问 -> 短TTL
                frequency_factor = min(pattern.access_frequency / 10.0, 3.0)  # 最大3倍
                smart_ttl = int(self.base_ttl * (1 + frequency_factor))
            else:
                smart_ttl = self.base_ttl
            
            # 基于访问间隔微调
            if pattern.avg_interval > 0:
                # 如果平均间隔很短，延长TTL
                if pattern.avg_interval < 60:  # 1分钟内
                    smart_ttl = int(smart_ttl * 1.5)
                elif pattern.avg_interval > 600:  # 10分钟以上
                    smart_ttl = int(smart_ttl * 0.8)
            
            # 限制在合理范围内
            smart_ttl = max(self.min_ttl, min(smart_ttl, self.max_ttl))
            
            self.cache_stats['ttl_adjustments'] += 1
            logger.debug(f"设备 {device_id} 智能TTL: {smart_ttl}s (频率: {pattern.access_frequency:.2f}/h)")
            
            return smart_ttl
    
    def should_preload(self, device_id: str) -> bool:
        """判断是否应该预加载"""
        with self.pattern_lock:
            if device_id not in self.access_patterns:
                return False
            
            pattern = self.access_patterns[device_id]
            
            # 基于访问频率判断
            if pattern.access_frequency >= self.preload_threshold:
                return True
            
            # 基于访问规律判断
            current_time = time.time()
            if (pattern.last_access_time > 0 and 
                pattern.avg_interval > 0 and
                current_time - pattern.last_access_time >= pattern.avg_interval * 0.8):
                return True
            
            return False
    
    def add_to_preload_queue(self, model_name: str, device_id: str, version: str) -> None:
        """添加到预加载队列"""
        with self.preload_lock:
            item = (model_name, device_id, version)
            if item not in self.preload_queue:
                self.preload_queue.append(item)
                logger.debug(f"添加到预加载队列: {model_name}:{device_id}:{version}")
    
    def get_preload_items(self, max_items: int = 5) -> List[Tuple[str, str, str]]:
        """获取预加载项目"""
        with self.preload_lock:
            items = self.preload_queue[:max_items]
            self.preload_queue = self.preload_queue[max_items:]
            return items
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.cache_stats['total_requests']
        if total_requests == 0:
            return self.cache_stats.copy()
        
        stats = self.cache_stats.copy()
        stats['hit_rate'] = self.cache_stats['hits'] / total_requests
        stats['miss_rate'] = self.cache_stats['misses'] / total_requests
        
        return stats
    
    def get_access_patterns_summary(self) -> Dict[str, Any]:
        """获取访问模式摘要"""
        with self.pattern_lock:
            summary = {
                'total_devices': len(self.access_patterns),
                'high_frequency_devices': 0,
                'preload_candidates': 0,
                'avg_frequency': 0.0
            }
            
            if not self.access_patterns:
                return summary
            
            frequencies = []
            for device_id, pattern in self.access_patterns.items():
                frequencies.append(pattern.access_frequency)
                
                if pattern.access_frequency >= self.preload_threshold:
                    summary['high_frequency_devices'] += 1
                
                if self.should_preload(device_id):
                    summary['preload_candidates'] += 1
            
            summary['avg_frequency'] = sum(frequencies) / len(frequencies)
            
            return summary
    
    def _start_background_tasks(self) -> None:
        """启动后台任务"""
        # 启动访问模式清理任务
        cleanup_thread = threading.Thread(target=self._cleanup_old_patterns, daemon=True)
        cleanup_thread.start()
        
        # 启动预加载分析任务
        preload_thread = threading.Thread(target=self._analyze_preload_candidates, daemon=True)
        preload_thread.start()
    
    def _cleanup_old_patterns(self) -> None:
        """清理过期的访问模式"""
        while True:
            try:
                time.sleep(3600)  # 每小时清理一次
                current_time = time.time()
                
                with self.pattern_lock:
                    expired_devices = []
                    for device_id, pattern in self.access_patterns.items():
                        # 如果超过24小时没有访问，清理记录
                        if current_time - pattern.last_access_time > 86400:
                            expired_devices.append(device_id)
                    
                    for device_id in expired_devices:
                        del self.access_patterns[device_id]
                        logger.debug(f"清理过期访问模式: {device_id}")
                
            except Exception as e:
                logger.error(f"清理访问模式时出错: {e}")
    
    def _analyze_preload_candidates(self) -> None:
        """分析预加载候选项"""
        while True:
            try:
                time.sleep(300)  # 每5分钟分析一次
                
                with self.pattern_lock:
                    for device_id, pattern in self.access_patterns.items():
                        if self.should_preload(device_id):
                            # 这里需要与具体的模型管理器集成
                            # 暂时只记录日志
                            logger.debug(f"预加载候选: {device_id}")
                
            except Exception as e:
                logger.error(f"分析预加载候选时出错: {e}")
    
    def save_patterns_to_file(self, filepath: str) -> None:
        """保存访问模式到文件"""
        try:
            with self.pattern_lock:
                data = {}
                for device_id, pattern in self.access_patterns.items():
                    data[device_id] = {
                        'access_count': pattern.access_count,
                        'last_access_time': pattern.last_access_time,
                        'access_frequency': pattern.access_frequency,
                        'avg_interval': pattern.avg_interval,
                        'access_times': list(pattern.access_times)
                    }
                
                with open(filepath, 'w') as f:
                    json.dump(data, f, indent=2)
                
                logger.info(f"访问模式已保存到: {filepath}")
        
        except Exception as e:
            logger.error(f"保存访问模式失败: {e}")
    
    def load_patterns_from_file(self, filepath: str) -> None:
        """从文件加载访问模式"""
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
            
            with self.pattern_lock:
                for device_id, pattern_data in data.items():
                    pattern = AccessPattern(device_id=device_id)
                    pattern.access_count = pattern_data.get('access_count', 0)
                    pattern.last_access_time = pattern_data.get('last_access_time', 0.0)
                    pattern.access_frequency = pattern_data.get('access_frequency', 0.0)
                    pattern.avg_interval = pattern_data.get('avg_interval', 0.0)
                    pattern.access_times = deque(pattern_data.get('access_times', []), maxlen=100)
                    
                    self.access_patterns[device_id] = pattern
            
            logger.info(f"访问模式已从文件加载: {filepath}")
        
        except Exception as e:
            logger.error(f"加载访问模式失败: {e}")

# 全局智能缓存管理器实例
smart_cache_manager = SmartCacheManager()
