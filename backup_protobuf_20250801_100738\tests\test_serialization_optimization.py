#!/usr/bin/env python3
"""
序列化优化测试脚本
验证MessagePack + LZ4优化的性能和兼容性
"""

import sys
import os
import time
import unittest
import tempfile
import shutil
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from DBUtil.serialization_manager import SerializationManager, SerializationMethod
from DBUtil.Redis import RedisModelManager

class TestSerializationOptimization(unittest.TestCase):
    """序列化优化测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_data = self._create_test_model_data()
        self.serializer = SerializationManager()
    
    def _create_test_model_data(self):
        """创建测试用的模型数据"""
        return {
            'model_type': 'shouwei_control',
            'device_id': 'test_device_001',
            'version': 'v1.0',
            'weights': {
                'layer1': [[0.1, 0.2, 0.3] * 50 for _ in range(30)],
                'layer2': [[0.4, 0.5, 0.6] * 40 for _ in range(25)],
                'output': [[0.7, 0.8] * 20 for _ in range(15)]
            },
            'metadata': {
                'accuracy': 0.95,
                'training_time': 3600,
                'features': [f'feature_{i}' for i in range(100)],
                'hyperparameters': {
                    'learning_rate': 0.001,
                    'batch_size': 32,
                    'epochs': 100
                }
            },
            'last_updated': time.time()
        }
    
    def test_pickle_compatibility(self):
        """测试Pickle兼容性"""
        # 使用Pickle序列化
        serialized = self.serializer.serialize(self.test_data, SerializationMethod.PICKLE)
        deserialized = self.serializer.deserialize(serialized, SerializationMethod.PICKLE)
        
        self.assertEqual(deserialized['device_id'], self.test_data['device_id'])
        self.assertEqual(len(deserialized['weights']['layer1']), len(self.test_data['weights']['layer1']))
    
    def test_msgpack_serialization(self):
        """测试MessagePack序列化"""
        try:
            # 使用MessagePack序列化
            serialized = self.serializer.serialize(self.test_data, SerializationMethod.MSGPACK)
            deserialized = self.serializer.deserialize(serialized, SerializationMethod.MSGPACK)
            
            self.assertEqual(deserialized['device_id'], self.test_data['device_id'])
            self.assertEqual(len(deserialized['weights']['layer1']), len(self.test_data['weights']['layer1']))
        except ImportError:
            self.skipTest("MessagePack库未安装")
    
    def test_msgpack_lz4_compression(self):
        """测试MessagePack + LZ4压缩"""
        try:
            # 使用MessagePack + LZ4序列化
            serialized = self.serializer.serialize(self.test_data, SerializationMethod.MSGPACK_LZ4)
            deserialized = self.serializer.deserialize(serialized, SerializationMethod.MSGPACK_LZ4)
            
            self.assertEqual(deserialized['device_id'], self.test_data['device_id'])
            self.assertEqual(len(deserialized['weights']['layer1']), len(self.test_data['weights']['layer1']))
        except ImportError:
            self.skipTest("MessagePack或LZ4库未安装")
    
    def test_auto_format_detection(self):
        """测试自动格式检测"""
        try:
            # 序列化不同格式的数据
            pickle_data = self.serializer.serialize(self.test_data, SerializationMethod.PICKLE)
            msgpack_data = self.serializer.serialize(self.test_data, SerializationMethod.MSGPACK)
            compressed_data = self.serializer.serialize(self.test_data, SerializationMethod.MSGPACK_LZ4)
            
            # 自动检测并反序列化
            pickle_result = self.serializer.deserialize(pickle_data)
            msgpack_result = self.serializer.deserialize(msgpack_data)
            compressed_result = self.serializer.deserialize(compressed_data)
            
            # 验证结果一致性
            self.assertEqual(pickle_result['device_id'], self.test_data['device_id'])
            self.assertEqual(msgpack_result['device_id'], self.test_data['device_id'])
            self.assertEqual(compressed_result['device_id'], self.test_data['device_id'])
        except ImportError:
            self.skipTest("优化库未安装")
    
    def test_performance_comparison(self):
        """测试性能对比"""
        iterations = 10
        
        # 测试Pickle性能
        start_time = time.time()
        for _ in range(iterations):
            serialized = self.serializer.serialize(self.test_data, SerializationMethod.PICKLE)
            self.serializer.deserialize(serialized, SerializationMethod.PICKLE)
        pickle_time = time.time() - start_time
        
        try:
            # 测试MessagePack + LZ4性能
            start_time = time.time()
            for _ in range(iterations):
                serialized = self.serializer.serialize(self.test_data, SerializationMethod.MSGPACK_LZ4)
                self.serializer.deserialize(serialized, SerializationMethod.MSGPACK_LZ4)
            optimized_time = time.time() - start_time
            
            print(f"\n性能对比 ({iterations}次迭代):")
            print(f"Pickle时间: {pickle_time:.4f}s")
            print(f"优化方案时间: {optimized_time:.4f}s")
            print(f"性能提升: {(pickle_time / optimized_time):.2f}x")
            
            # 验证优化方案不会显著降低性能
            self.assertLess(optimized_time, pickle_time * 2, "优化方案性能不应显著低于Pickle")
        except ImportError:
            self.skipTest("优化库未安装")
    
    def test_compression_ratio(self):
        """测试压缩比"""
        try:
            # 获取不同方法的数据大小
            pickle_data = self.serializer.serialize(self.test_data, SerializationMethod.PICKLE)
            msgpack_data = self.serializer.serialize(self.test_data, SerializationMethod.MSGPACK)
            compressed_data = self.serializer.serialize(self.test_data, SerializationMethod.MSGPACK_LZ4)
            
            pickle_size = len(pickle_data)
            msgpack_size = len(msgpack_data)
            compressed_size = len(compressed_data)
            
            print(f"\n数据大小对比:")
            print(f"Pickle: {pickle_size:,} bytes")
            print(f"MessagePack: {msgpack_size:,} bytes")
            print(f"MessagePack+LZ4: {compressed_size:,} bytes")
            print(f"压缩比: {compressed_size/pickle_size:.2f}")
            print(f"空间节省: {(1-compressed_size/pickle_size)*100:.1f}%")
            
            # 验证压缩确实减少了数据大小
            self.assertLess(compressed_size, pickle_size, "压缩后数据应该更小")
        except ImportError:
            self.skipTest("优化库未安装")
    
    def test_fallback_mechanism(self):
        """测试降级机制"""
        # 模拟优化库不可用的情况
        with patch('DBUtil.serialization_manager.OPTIMIZATION_AVAILABLE', False):
            fallback_serializer = SerializationManager()
            
            # 应该自动降级到Pickle
            serialized = fallback_serializer.serialize(self.test_data)
            deserialized = fallback_serializer.deserialize(serialized)
            
            self.assertEqual(deserialized['device_id'], self.test_data['device_id'])
    
    def test_redis_integration(self):
        """测试Redis集成"""
        # 模拟Redis连接
        mock_redis = Mock()
        mock_redis.get.return_value = None
        mock_redis.set.return_value = True
        
        with patch('redis.StrictRedis', return_value=mock_redis):
            redis_manager = RedisModelManager(
                redis_host='localhost',
                redis_port=6379,
                redis_password='',
                redis_db=0
            )
            
            # 测试保存和加载
            redis_manager.save_model('test_model', 'device_001', self.test_data, 'v1')
            
            # 验证Redis被调用
            self.assertTrue(mock_redis.set.called)
    
    def test_statistics_collection(self):
        """测试统计信息收集"""
        # 执行一些序列化操作
        for _ in range(5):
            serialized = self.serializer.serialize(self.test_data)
            self.serializer.deserialize(serialized)
        
        # 获取统计信息
        stats = self.serializer.get_stats()
        
        if stats:  # 如果启用了统计
            self.assertGreater(stats['serialize_count'], 0)
            self.assertGreater(stats['deserialize_count'], 0)
            self.assertGreaterEqual(stats['avg_serialize_time_ms'], 0)
            self.assertGreaterEqual(stats['avg_deserialize_time_ms'], 0)

class TestRealWorldScenarios(unittest.TestCase):
    """真实场景测试"""
    
    def test_shouwei_model_data(self):
        """测试首尾控制模型数据"""
        shouwei_data = {
            'model_type': 'shouwei_lasu_control',
            'device_id': 'shouwei_001',
            'power_baseline': [100.0, 105.0, 110.0, 115.0],
            'lasu_baseline': [2.5, 2.6, 2.7, 2.8],
            'weight_corrections': {
                'weight_11': [0.1, 0.2, 0.3],
                'weight_12': [0.15, 0.25, 0.35],
                'weight_105': [0.05, 0.1, 0.15]
            },
            'last_loaded': time.time()
        }
        
        serializer = SerializationManager()
        
        # 测试序列化和反序列化
        serialized = serializer.serialize(shouwei_data)
        deserialized = serializer.deserialize(serialized)
        
        self.assertEqual(deserialized['device_id'], shouwei_data['device_id'])
        self.assertEqual(len(deserialized['power_baseline']), len(shouwei_data['power_baseline']))
    
    def test_kongwen_model_data(self):
        """测试控温模型数据"""
        kongwen_data = {
            'model_type': 'kongwen_power_control',
            'device_id': 'kongwen_001',
            'main_power_model': {
                'coefficients': [1.2, 0.8, 0.5, 0.3],
                'intercept': 50.0
            },
            'vice_power_model': {
                'coefficients': [0.8, 0.6, 0.4, 0.2],
                'intercept': 30.0
            },
            'feature_names': ['t', 'ratio', 'ccd', 'ccd3', 'fullmelting'],
            'last_loaded': time.time()
        }
        
        serializer = SerializationManager()
        
        # 测试序列化和反序列化
        serialized = serializer.serialize(kongwen_data)
        deserialized = serializer.deserialize(serialized)
        
        self.assertEqual(deserialized['device_id'], kongwen_data['device_id'])
        self.assertEqual(len(deserialized['feature_names']), len(kongwen_data['feature_names']))

def run_performance_benchmark():
    """运行性能基准测试"""
    print("=" * 60)
    print("序列化优化性能基准测试")
    print("=" * 60)
    
    # 创建测试数据
    test_data = {
        'weights': [[0.1] * 100 for _ in range(100)],
        'metadata': {'features': [f'f_{i}' for i in range(1000)]}
    }
    
    serializer = SerializationManager()
    iterations = 100
    
    methods = [
        (SerializationMethod.PICKLE, "Pickle"),
        (SerializationMethod.MSGPACK, "MessagePack"),
        (SerializationMethod.MSGPACK_LZ4, "MessagePack+LZ4")
    ]
    
    for method, name in methods:
        try:
            start_time = time.time()
            total_size = 0
            
            for _ in range(iterations):
                serialized = serializer.serialize(test_data, method)
                total_size += len(serialized)
                serializer.deserialize(serialized, method)
            
            elapsed_time = time.time() - start_time
            avg_size = total_size / iterations
            
            print(f"{name}:")
            print(f"  总时间: {elapsed_time:.4f}s")
            print(f"  平均每次: {elapsed_time/iterations*1000:.2f}ms")
            print(f"  平均大小: {avg_size:,.0f} bytes")
            print()
            
        except ImportError:
            print(f"{name}: 库未安装，跳过测试")
            print()

if __name__ == '__main__':
    # 运行性能基准测试
    run_performance_benchmark()
    
    # 运行单元测试
    unittest.main(verbosity=2)
