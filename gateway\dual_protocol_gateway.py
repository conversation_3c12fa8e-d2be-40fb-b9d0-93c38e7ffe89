#!/usr/bin/env python3
"""
双协议网关
同时支持HTTP REST API和gRPC协议
"""

import sys
import os
import json
import time
import logging
import threading
from typing import Dict, Any, Optional
from flask import Flask, request, jsonify
from concurrent import futures

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import grpc
    from grpc_generated import model_service_pb2
    from grpc_generated import model_service_pb2_grpc
    GRPC_AVAILABLE = True
except ImportError:
    GRPC_AVAILABLE = False
    print("gRPC库未安装，仅支持HTTP协议")

logger = logging.getLogger(__name__)

class DualProtocolGateway:
    """双协议网关"""
    
    def __init__(self, 
                 http_port: int = 8080,
                 grpc_port: int = 50051,
                 grpc_enabled: bool = True):
        
        self.http_port = http_port
        self.grpc_port = grpc_port
        self.grpc_enabled = grpc_enabled and GRPC_AVAILABLE
        
        # Flask应用
        self.app = Flask(__name__)
        self.app.logger.setLevel(logging.INFO)
        
        # gRPC客户端（用于转发请求）
        self.grpc_client = None
        if self.grpc_enabled:
            self._initialize_grpc_client()
        
        # 统计信息
        self.stats = {
            'http_requests': 0,
            'grpc_requests': 0,
            'http_errors': 0,
            'grpc_errors': 0,
            'total_response_time': 0.0
        }
        self.stats_lock = threading.RLock()
        
        # 注册HTTP路由
        self._register_http_routes()
    
    def _initialize_grpc_client(self):
        """初始化gRPC客户端"""
        try:
            channel = grpc.insecure_channel(f'localhost:{self.grpc_port}')
            self.grpc_client = model_service_pb2_grpc.ModelServiceStub(channel)
            logger.info(f"gRPC客户端已连接到端口: {self.grpc_port}")
        except Exception as e:
            logger.error(f"gRPC客户端初始化失败: {e}")
            self.grpc_enabled = False
    
    def _register_http_routes(self):
        """注册HTTP路由"""
        
        @self.app.route('/predict', methods=['POST'])
        def http_predict():
            """HTTP预测接口"""
            start_time = time.time()
            
            try:
                with self.stats_lock:
                    self.stats['http_requests'] += 1
                
                # 解析请求
                data = request.get_json()
                if not data:
                    return jsonify({'error': '无效的JSON数据'}), 400
                
                # 如果启用了gRPC，转发到gRPC服务
                if self.grpc_enabled and self.grpc_client:
                    result = self._forward_to_grpc_predict(data)
                else:
                    # 直接调用现有的HTTP逻辑
                    result = self._call_existing_http_logic(data)
                
                response_time = time.time() - start_time
                with self.stats_lock:
                    self.stats['total_response_time'] += response_time
                
                return jsonify(result)
                
            except Exception as e:
                with self.stats_lock:
                    self.stats['http_errors'] += 1
                
                logger.error(f"HTTP预测失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/setup', methods=['POST'])
        def http_setup():
            """HTTP模型设置接口"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'error': '无效的JSON数据'}), 400
                
                if self.grpc_enabled and self.grpc_client:
                    result = self._forward_to_grpc_setup(data)
                else:
                    result = self._call_existing_http_setup(data)
                
                return jsonify(result)
                
            except Exception as e:
                logger.error(f"HTTP设置失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/health', methods=['GET'])
        def http_health():
            """HTTP健康检查接口"""
            try:
                health_status = {
                    'status': 'healthy',
                    'timestamp': int(time.time() * 1000),
                    'protocols': {
                        'http': True,
                        'grpc': self.grpc_enabled
                    }
                }
                
                # 检查gRPC服务健康状态
                if self.grpc_enabled and self.grpc_client:
                    try:
                        grpc_health = self.grpc_client.HealthCheck(
                            model_service_pb2.HealthCheckRequest(service="model_service")
                        )
                        health_status['grpc_status'] = grpc_health.status
                    except Exception as e:
                        health_status['grpc_error'] = str(e)
                
                return jsonify(health_status)
                
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/stats', methods=['GET'])
        def http_stats():
            """HTTP统计信息接口"""
            try:
                with self.stats_lock:
                    stats = self.stats.copy()
                
                # 添加gRPC统计（如果可用）
                if self.grpc_enabled and self.grpc_client:
                    try:
                        grpc_stats = self.grpc_client.GetStats(
                            model_service_pb2.GetStatsRequest(include_detailed=True)
                        )
                        if grpc_stats.success:
                            stats['grpc_service_stats'] = {
                                'total_requests': grpc_stats.stats.total_requests,
                                'successful_requests': grpc_stats.stats.successful_requests,
                                'failed_requests': grpc_stats.stats.failed_requests,
                                'avg_response_time_ms': grpc_stats.stats.avg_response_time_ms,
                                'active_models': grpc_stats.stats.active_models,
                                'uptime_seconds': grpc_stats.stats.uptime_seconds
                            }
                    except Exception as e:
                        stats['grpc_stats_error'] = str(e)
                
                return jsonify(stats)
                
            except Exception as e:
                return jsonify({'error': str(e)}), 500
    
    def _forward_to_grpc_predict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """转发预测请求到gRPC服务"""
        try:
            with self.stats_lock:
                self.stats['grpc_requests'] += 1
            
            # 构建gRPC请求
            grpc_request = model_service_pb2.PredictRequest(
                model_name=data.get('model_name', ''),
                device_id=data.get('device_id', ''),
                version=data.get('version', 'v1'),
                features=data.get('features', {}),
                timestamp=int(time.time() * 1000),
                request_id=data.get('request_id', '')
            )
            
            # 调用gRPC服务
            response = self.grpc_client.Predict(grpc_request)
            
            if response.success:
                return {
                    'success': True,
                    'predictions': dict(response.predictions),
                    'processing_time_ms': response.processing_time_ms,
                    'model_version': response.model_version,
                    'protocol': 'grpc'
                }
            else:
                with self.stats_lock:
                    self.stats['grpc_errors'] += 1
                return {
                    'success': False,
                    'error': response.error_message,
                    'protocol': 'grpc'
                }
                
        except Exception as e:
            with self.stats_lock:
                self.stats['grpc_errors'] += 1
            logger.error(f"gRPC转发失败: {e}")
            raise
    
    def _forward_to_grpc_setup(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """转发设置请求到gRPC服务"""
        try:
            # 构建模型配置
            config = model_service_pb2.ModelConfig(
                product_type=data.get('product_type', ''),
                field_size=data.get('field_size', ''),
                power=data.get('power', 0.0),
                diameter=data.get('diameter', 0.0),
                weight=data.get('weight', 0.0),
                target_dia=data.get('target_dia', []),
                target_power=data.get('target_power', [])
            )
            
            grpc_request = model_service_pb2.SetupModelRequest(
                model_name=data.get('model_name', ''),
                device_id=data.get('device_id', ''),
                version=data.get('version', 'v1'),
                config=config,
                parameters=data.get('parameters', {}),
                timestamp=int(time.time() * 1000)
            )
            
            response = self.grpc_client.SetupModel(grpc_request)
            
            if response.success:
                return {
                    'success': True,
                    'baseline_values': dict(response.baseline_values),
                    'processing_time_ms': response.processing_time_ms,
                    'protocol': 'grpc'
                }
            else:
                return {
                    'success': False,
                    'error': response.error_message,
                    'protocol': 'grpc'
                }
                
        except Exception as e:
            logger.error(f"gRPC设置转发失败: {e}")
            raise
    
    def _call_existing_http_logic(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """调用现有的HTTP逻辑"""
        # 这里应该调用现有的main.py中的逻辑
        # 为了简化，这里返回模拟结果
        return {
            'success': True,
            'predictions': {'power': 100.0, 'speed': 2.5},
            'protocol': 'http_legacy'
        }
    
    def _call_existing_http_setup(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """调用现有的HTTP设置逻辑"""
        # 这里应该调用现有的main.py中的设置逻辑
        return {
            'success': True,
            'baseline_values': {'baseline_power': 100.0, 'baseline_speed': 2.5},
            'protocol': 'http_legacy'
        }
    
    def start_http_server(self):
        """启动HTTP服务器"""
        logger.info(f"启动HTTP服务器，端口: {self.http_port}")
        self.app.run(
            host='0.0.0.0',
            port=self.http_port,
            debug=False,
            threaded=True
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取网关统计信息"""
        with self.stats_lock:
            stats = self.stats.copy()
            
            total_requests = stats['http_requests'] + stats['grpc_requests']
            if total_requests > 0:
                stats['avg_response_time'] = stats['total_response_time'] / total_requests
                stats['http_success_rate'] = (stats['http_requests'] - stats['http_errors']) / stats['http_requests']
                stats['grpc_success_rate'] = (stats['grpc_requests'] - stats['grpc_errors']) / max(1, stats['grpc_requests'])
            
            return stats

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="双协议网关")
    parser.add_argument("--http-port", type=int, default=8080, help="HTTP端口")
    parser.add_argument("--grpc-port", type=int, default=50051, help="gRPC端口")
    parser.add_argument("--disable-grpc", action="store_true", help="禁用gRPC支持")
    
    args = parser.parse_args()
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建并启动网关
    gateway = DualProtocolGateway(
        http_port=args.http_port,
        grpc_port=args.grpc_port,
        grpc_enabled=not args.disable_grpc
    )
    
    try:
        gateway.start_http_server()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭网关...")

if __name__ == '__main__':
    main()
