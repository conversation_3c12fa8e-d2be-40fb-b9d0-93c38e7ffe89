#!/usr/bin/env python3
"""
Protobuf集成测试
测试Protobuf序列化和API功能
"""

import unittest
import sys
import os
import time
import json

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from DBUtil.protobuf_serializer import protobuf_serialization_manager, ExtendedSerializationMethod
from DBUtil.Redis import RedisModelManager

class TestProtobufSerialization(unittest.TestCase):
    """测试Protobuf序列化功能"""
    
    def setUp(self):
        """测试设置"""
        self.serializer = protobuf_serialization_manager
        self.test_data = {
            'device_id': 'test_device_001',
            'temperature': 25.5,
            'pressure': 1.2,
            'flow_rate': 15.8,
            'power_consumption': 850.0,
            'timestamp': time.time()
        }
    
    def test_serialization_methods_available(self):
        """测试序列化方法是否可用"""
        methods = [
            ExtendedSerializationMethod.PICKLE,
            ExtendedSerializationMethod.MSGPACK,
            ExtendedSerializationMethod.MSGPACK_LZ4,
            ExtendedSerializationMethod.PROTOBUF,
            ExtendedSerializationMethod.PROTOBUF_LZ4
        ]
        
        for method in methods:
            with self.subTest(method=method):
                self.assertIsInstance(method, ExtendedSerializationMethod)
    
    def test_basic_serialization(self):
        """测试基础序列化功能"""
        # 测试不同的序列化方法
        methods = [
            ExtendedSerializationMethod.PICKLE,
            ExtendedSerializationMethod.MSGPACK,
            ExtendedSerializationMethod.MSGPACK_LZ4
        ]
        
        for method in methods:
            with self.subTest(method=method):
                try:
                    # 序列化
                    serialized = self.serializer.serialize(self.test_data, method)
                    self.assertIsInstance(serialized, bytes)
                    self.assertGreater(len(serialized), 0)
                    
                    # 反序列化
                    deserialized = self.serializer.deserialize(serialized, method)
                    self.assertEqual(deserialized['device_id'], self.test_data['device_id'])
                    self.assertAlmostEqual(deserialized['temperature'], self.test_data['temperature'])
                    
                except Exception as e:
                    self.fail(f"序列化测试失败 {method}: {e}")
    
    def test_format_detection(self):
        """测试格式自动检测"""
        # 测试不同格式的自动检测
        test_cases = [
            (ExtendedSerializationMethod.PICKLE, self.test_data),
            (ExtendedSerializationMethod.MSGPACK, self.test_data),
            (ExtendedSerializationMethod.MSGPACK_LZ4, self.test_data)
        ]
        
        for method, data in test_cases:
            with self.subTest(method=method):
                try:
                    # 序列化
                    serialized = self.serializer.serialize(data, method)
                    
                    # 自动检测并反序列化
                    detected_data = self.serializer.deserialize(serialized)
                    
                    # 验证数据完整性
                    self.assertEqual(detected_data['device_id'], data['device_id'])
                    
                except Exception as e:
                    self.fail(f"格式检测测试失败 {method}: {e}")
    
    def test_fallback_mechanism(self):
        """测试降级机制"""
        # 创建启用降级的序列化管理器
        serializer = protobuf_serialization_manager
        
        # 测试正常情况
        try:
            serialized = serializer.serialize(self.test_data, ExtendedSerializationMethod.PICKLE)
            deserialized = serializer.deserialize(serialized)
            self.assertEqual(deserialized['device_id'], self.test_data['device_id'])
        except Exception as e:
            self.fail(f"降级机制测试失败: {e}")
    
    def test_performance_stats(self):
        """测试性能统计功能"""
        # 执行一些序列化操作
        for _ in range(5):
            serialized = self.serializer.serialize(self.test_data, ExtendedSerializationMethod.MSGPACK)
            self.serializer.deserialize(serialized, ExtendedSerializationMethod.MSGPACK)
        
        # 获取统计信息
        stats = self.serializer.get_stats()
        
        # 验证统计信息
        self.assertIsInstance(stats, dict)
        self.assertIn('serialize_count', stats)
        self.assertIn('deserialize_count', stats)
        self.assertGreaterEqual(stats['serialize_count'], 5)
        self.assertGreaterEqual(stats['deserialize_count'], 5)

class TestProtobufMessages(unittest.TestCase):
    """测试Protobuf消息功能"""
    
    def setUp(self):
        """测试设置"""
        try:
            from DBUtil.industrial_control.v1 import common_pb2
            from DBUtil.industrial_control.v1 import shouwei_service_pb2
            from DBUtil.industrial_control.v1 import kongwen_service_pb2
            self.protobuf_available = True
            self.common_pb2 = common_pb2
            self.shouwei_pb2 = shouwei_service_pb2
            self.kongwen_pb2 = kongwen_service_pb2
        except ImportError:
            self.protobuf_available = False
    
    def test_protobuf_messages_available(self):
        """测试Protobuf消息是否可用"""
        if not self.protobuf_available:
            self.skipTest("Protobuf消息不可用")
        
        # 测试创建基础消息
        device_info = self.common_pb2.DeviceInfo()
        device_info.device_id = "test_device"
        device_info.device_type = "shouwei_lasu_control"
        
        self.assertEqual(device_info.device_id, "test_device")
        self.assertEqual(device_info.device_type, "shouwei_lasu_control")
    
    def test_shouwei_request_creation(self):
        """测试首尾控制请求创建"""
        if not self.protobuf_available:
            self.skipTest("Protobuf消息不可用")
        
        # 创建首尾控制请求
        request = self.shouwei_pb2.ShouweiPredictionRequest()
        
        # 填充数据
        request.device_info.device_id = "shouwei_001"
        request.sensor_data.temperature = 25.5
        request.sensor_data.pressure = 1.2
        request.control_parameters.target_temperature = 26.0
        request.shouwei_data.lasu_coefficient = 1.25
        
        # 验证数据
        self.assertEqual(request.device_info.device_id, "shouwei_001")
        self.assertAlmostEqual(request.sensor_data.temperature, 25.5)
        self.assertAlmostEqual(request.shouwei_data.lasu_coefficient, 1.25)
    
    def test_kongwen_request_creation(self):
        """测试控温请求创建"""
        if not self.protobuf_available:
            self.skipTest("Protobuf消息不可用")
        
        # 创建控温请求
        request = self.kongwen_pb2.KongwenPredictionRequest()
        
        # 填充数据
        request.device_info.device_id = "kongwen_001"
        request.sensor_data.temperature = 24.8
        request.kongwen_data.thermal_capacity = 1000.0
        
        # 验证数据
        self.assertEqual(request.device_info.device_id, "kongwen_001")
        self.assertAlmostEqual(request.sensor_data.temperature, 24.8)
        self.assertAlmostEqual(request.kongwen_data.thermal_capacity, 1000.0)
    
    def test_message_serialization(self):
        """测试消息序列化"""
        if not self.protobuf_available:
            self.skipTest("Protobuf消息不可用")
        
        # 创建并序列化消息
        request = self.shouwei_pb2.ShouweiPredictionRequest()
        request.device_info.device_id = "test_device"
        request.sensor_data.temperature = 25.5
        
        # 序列化
        serialized = request.SerializeToString()
        self.assertIsInstance(serialized, bytes)
        self.assertGreater(len(serialized), 0)
        
        # 反序列化
        new_request = self.shouwei_pb2.ShouweiPredictionRequest()
        new_request.ParseFromString(serialized)
        
        # 验证数据
        self.assertEqual(new_request.device_info.device_id, "test_device")
        self.assertAlmostEqual(new_request.sensor_data.temperature, 25.5)

class TestRedisProtobufIntegration(unittest.TestCase):
    """测试Redis Protobuf集成"""
    
    def setUp(self):
        """测试设置"""
        # 使用测试Redis配置
        self.redis_manager = RedisModelManager(
            redis_host='localhost',
            redis_port=6379,
            redis_password='',
            redis_db=15,  # 使用测试数据库
            enable_protobuf=True,
            serialization_method=ExtendedSerializationMethod.PROTOBUF_LZ4
        )
        
        self.test_model_data = {
            'model_type': 'shouwei_lasu_control',
            'version': 'v1.0',
            'parameters': {
                'weights': [0.1, 0.2, 0.3, 0.4],
                'bias': 0.05,
                'learning_rate': 0.001
            },
            'metadata': {
                'created_time': time.time(),
                'accuracy': 0.95
            }
        }
    
    def test_redis_protobuf_save_load(self):
        """测试Redis Protobuf保存和加载"""
        model_name = "test_model"
        device_id = "test_device_001"
        version = "v1.0"
        
        try:
            # 保存模型
            success = self.redis_manager.save_model(
                model_name, device_id, self.test_model_data, version
            )
            self.assertTrue(success, "模型保存失败")
            
            # 加载模型
            loaded_model = self.redis_manager.load_model(model_name, device_id, version)
            self.assertIsNotNone(loaded_model, "模型加载失败")
            
            # 验证数据完整性
            self.assertEqual(loaded_model['model_type'], self.test_model_data['model_type'])
            self.assertEqual(loaded_model['version'], self.test_model_data['version'])
            self.assertEqual(loaded_model['parameters']['weights'], 
                           self.test_model_data['parameters']['weights'])
            
        except Exception as e:
            self.fail(f"Redis Protobuf集成测试失败: {e}")
        
        finally:
            # 清理测试数据
            try:
                self.redis_manager.delete_model(model_name, device_id, version)
            except:
                pass
    
    def test_serialization_method_selection(self):
        """测试序列化方法选择"""
        # 测试不同的序列化方法
        methods = [
            ExtendedSerializationMethod.PROTOBUF,
            ExtendedSerializationMethod.PROTOBUF_LZ4,
            ExtendedSerializationMethod.MSGPACK_LZ4
        ]
        
        for method in methods:
            with self.subTest(method=method):
                # 创建使用特定方法的Redis管理器
                redis_manager = RedisModelManager(
                    redis_host='localhost',
                    redis_port=6379,
                    redis_password='',
                    redis_db=15,
                    enable_protobuf=True,
                    serialization_method=method
                )
                
                model_name = f"test_model_{method.value}"
                device_id = "test_device"
                version = "v1.0"
                
                try:
                    # 保存和加载
                    success = redis_manager.save_model(
                        model_name, device_id, self.test_model_data, version
                    )
                    self.assertTrue(success)
                    
                    loaded_model = redis_manager.load_model(model_name, device_id, version)
                    self.assertIsNotNone(loaded_model)
                    self.assertEqual(loaded_model['model_type'], self.test_model_data['model_type'])
                    
                except Exception as e:
                    self.fail(f"序列化方法测试失败 {method}: {e}")
                
                finally:
                    try:
                        redis_manager.delete_model(model_name, device_id, version)
                    except:
                        pass

def run_performance_comparison():
    """运行性能对比测试"""
    print("\n=== Protobuf性能对比测试 ===")
    
    # 测试数据
    test_data = {
        'device_id': 'performance_test_device',
        'sensor_data': {
            'temperature': 25.5,
            'pressure': 1.2,
            'flow_rate': 15.8,
            'power_consumption': 850.0,
            'humidity': 65.2,
            'vibration': 0.05
        },
        'control_parameters': {
            'target_temperature': 26.0,
            'max_power_limit': 1000,
            'control_precision': 0.1,
            'emergency_stop': False
        },
        'metadata': {
            'timestamp': time.time(),
            'version': 'v1.0',
            'accuracy': 0.95
        }
    }
    
    # 测试方法
    methods = [
        ExtendedSerializationMethod.PICKLE,
        ExtendedSerializationMethod.MSGPACK,
        ExtendedSerializationMethod.MSGPACK_LZ4,
        ExtendedSerializationMethod.PROTOBUF,
        ExtendedSerializationMethod.PROTOBUF_LZ4
    ]
    
    iterations = 1000
    results = {}
    
    for method in methods:
        try:
            # 序列化性能测试
            start_time = time.time()
            for _ in range(iterations):
                serialized = protobuf_serialization_manager.serialize(test_data, method)
            serialize_time = time.time() - start_time
            
            # 反序列化性能测试
            start_time = time.time()
            for _ in range(iterations):
                deserialized = protobuf_serialization_manager.deserialize(serialized, method)
            deserialize_time = time.time() - start_time
            
            # 数据大小
            data_size = len(serialized)
            
            results[method.value] = {
                'serialize_time': serialize_time,
                'deserialize_time': deserialize_time,
                'total_time': serialize_time + deserialize_time,
                'data_size': data_size,
                'serialize_ops_per_sec': iterations / serialize_time,
                'deserialize_ops_per_sec': iterations / deserialize_time
            }
            
        except Exception as e:
            print(f"性能测试失败 {method}: {e}")
            results[method.value] = {'error': str(e)}
    
    # 输出结果
    print(f"\n性能测试结果 ({iterations} 次迭代):")
    print("-" * 80)
    print(f"{'方法':<15} {'序列化时间':<12} {'反序列化时间':<14} {'数据大小':<10} {'总时间':<10}")
    print("-" * 80)
    
    for method, result in results.items():
        if 'error' not in result:
            print(f"{method:<15} {result['serialize_time']:<12.3f} "
                  f"{result['deserialize_time']:<14.3f} {result['data_size']:<10} "
                  f"{result['total_time']:<10.3f}")
        else:
            print(f"{method:<15} ERROR: {result['error']}")
    
    return results

if __name__ == '__main__':
    # 运行单元测试
    print("运行Protobuf集成测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行性能对比测试
    run_performance_comparison()
